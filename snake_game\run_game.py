#!/usr/bin/env python3
"""
贪吃蛇游戏启动脚本
"""

import sys
import os

def check_pygame():
    """检查pygame是否已安装"""
    try:
        import pygame
        return True
    except ImportError:
        return False

def install_pygame():
    """提示安装pygame"""
    print("检测到pygame未安装。")
    print("请运行以下命令安装pygame:")
    print("pip install pygame")
    print("\n或者使用conda:")
    print("conda install pygame")

def main():
    """主函数"""
    print("=" * 50)
    print("🐍 贪吃蛇游戏启动器")
    print("=" * 50)
    
    # 检查pygame
    if not check_pygame():
        install_pygame()
        return
    
    # 导入并运行游戏
    try:
        from snake_game import main as game_main
        print("正在启动游戏...")
        game_main()
    except Exception as e:
        print(f"游戏启动失败: {e}")
        print("请确保所有文件都在正确的位置。")

if __name__ == "__main__":
    main()
