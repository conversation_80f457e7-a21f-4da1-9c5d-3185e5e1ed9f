#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版3D彩票数据爬虫和预测系统
功能：
1. 爬取最新3D彩票数据
2. 运行多种预测算法
3. 将预测结果写入Excel文件
4. 支持横向对比和准确率统计
"""

import requests
import pandas as pd
import os
import time
import json
import re
from bs4 import BeautifulSoup
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from datetime import datetime
import sys

# 导入分析模块
try:
    from simple_analysis import Simple3DAnalyzer
except ImportError as e:
    print(f"导入simple_analysis模块失败: {e}")
    Simple3DAnalyzer = None

try:
    from markov_analysis import MarkovChain3D
except ImportError as e:
    print(f"导入markov_analysis模块失败: {e}")
    MarkovChain3D = None

try:
    from ml_prediction import SimpleMachineLearning3D
except ImportError as e:
    print(f"导入ml_prediction模块失败: {e}")
    SimpleMachineLearning3D = None

class Enhanced3DSpider:
    """增强版3D彩票爬虫和预测系统"""
    
    def __init__(self, data_dir="analysisball/3d"):
        """初始化爬虫"""
        self.data_dir = data_dir
        self.excel_file = os.path.join(data_dir, "3d.xlsx")
        self.session = self._create_session()
        
        # 确保目录存在
        os.makedirs(data_dir, exist_ok=True)
        
        # 初始化分析器
        self.analyzers = {}
        self._init_analyzers()
    
    def _create_session(self):
        """创建带重试机制的会话"""
        session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        return session
    
    def _init_analyzers(self):
        """初始化分析器"""
        if os.path.exists(self.excel_file):
            # 初始化简单分析器
            if Simple3DAnalyzer:
                try:
                    self.analyzers['simple'] = Simple3DAnalyzer(self.excel_file)
                    print("[成功] 简单分析器初始化成功")
                except Exception as e:
                    print(f"[失败] 简单分析器初始化失败: {e}")

            # 初始化马尔科夫链分析器
            if MarkovChain3D:
                try:
                    self.analyzers['markov'] = MarkovChain3D(self.excel_file)
                    print("[成功] 马尔科夫链分析器初始化成功")
                except Exception as e:
                    print(f"[失败] 马尔科夫链分析器初始化失败: {e}")

            # 初始化机器学习分析器
            if SimpleMachineLearning3D:
                try:
                    self.analyzers['ml'] = SimpleMachineLearning3D(self.excel_file)
                    print("[成功] 机器学习分析器初始化成功")
                except Exception as e:
                    print(f"[失败] 机器学习分析器初始化失败: {e}")
        else:
            print("Excel文件不存在，跳过分析器初始化")
    
    def get_lottery_data(self, last_period=None, num_periods=100):
        """获取彩票数据"""
        base_url = "https://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice"
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Referer": "https://www.cwl.gov.cn/ygkj/wqkjgg/3d/",
            "X-Requested-With": "XMLHttpRequest"
        }
        
        data = []
        page = 1
        page_size = 30
        
        print(f"开始获取3D彩票数据，目标期数: {num_periods}")
        
        while len(data) < num_periods:
            params = {
                "name": "3d",
                "issueCount": "",
                "issueStart": "",
                "issueEnd": "",
                "pageNo": page,
                "pageSize": page_size,
                "systemType": "PC"
            }
            
            try:
                response = self.session.get(base_url, headers=headers, params=params, timeout=10)
                response.raise_for_status()
            except requests.exceptions.RequestException as e:
                print(f"页面 {page} 请求失败: {e}")
                break
            
            try:
                json_data = response.json()
            except json.JSONDecodeError:
                print(f"页面 {page} 返回的不是有效的JSON")
                break
            
            draw_notice_list = json_data.get("result", [])
            
            if not draw_notice_list:
                print(f"页面 {page} 没有找到数据")
                break
            
            for item in draw_notice_list:
                issue = item.get("code", "")
                date = item.get("date", "")
                numbers = item.get("red", "")
                
                if issue and date and numbers and len(numbers) == 3:
                    # 如果指定了last_period，只获取比它新的数据
                    if last_period and int(issue) <= last_period:
                        print(f"已获取到指定期号 {last_period}，停止获取")
                        return data
                    
                    # 解析数字
                    hundreds = int(numbers[0])
                    tens = int(numbers[1])
                    units = int(numbers[2])
                    
                    data.append([
                        date,
                        int(issue),
                        hundreds,
                        tens,
                        units,
                        [numbers[0], numbers[1], numbers[2]]
                    ])
                    
                    if len(data) >= num_periods:
                        break
            
            page += 1
            time.sleep(0.5)  # 避免请求过快
        
        print(f"成功获取 {len(data)} 期数据")
        return data
    
    def save_to_excel_with_predictions(self, data):
        """保存数据到Excel并添加预测结果"""
        if not data:
            print("没有新数据需要保存")
            return
        
        # 创建基本数据框
        new_df = pd.DataFrame(data, columns=["日期", "期号", "百位", "十位", "个位", "奖号"])
        new_df["期号"] = pd.to_numeric(new_df["期号"])
        
        # 如果已存在Excel文件，合并数据
        if os.path.exists(self.excel_file):
            existing_df = pd.read_excel(self.excel_file)
            existing_df["期号"] = pd.to_numeric(existing_df["期号"])
            
            # 合并数据
            merged_df = pd.concat([new_df, existing_df])
            result_df = merged_df.drop_duplicates(subset=["期号"], keep="first")
            combined_df = result_df.sort_values(by="期号", ascending=False)
        else:
            combined_df = new_df
        
        # 添加预测结果列（如果不存在）
        self._add_prediction_columns(combined_df)
        
        # 为新数据生成预测结果
        self._generate_predictions_for_new_data(combined_df, new_df)
        
        # 保存到Excel
        combined_df.to_excel(self.excel_file, index=False)
        print(f"数据已保存到 {self.excel_file}")
        print(f"总数据条数: {len(combined_df)}")
        
        return combined_df
    
    def _add_prediction_columns(self, df):
        """添加预测结果列"""
        prediction_columns = [
            # 简单分析预测列
            "简单_百位_预测", "简单_百位_概率",
            "简单_十位_预测", "简单_十位_概率",
            "简单_个位_预测", "简单_个位_概率",

            # 马尔科夫链预测列
            "马尔科夫_百位_预测", "马尔科夫_百位_概率",
            "马尔科夫_十位_预测", "马尔科夫_十位_概率",
            "马尔科夫_个位_预测", "马尔科夫_个位_概率",

            # 机器学习预测列
            "ML_朴素贝叶斯_百位", "ML_朴素贝叶斯_十位", "ML_朴素贝叶斯_个位",
            "ML_决策树_百位", "ML_决策树_十位", "ML_决策树_个位",
            "ML_神经网络_百位", "ML_神经网络_十位", "ML_神经网络_个位",

            # 综合预测列
            "综合_百位_预测", "综合_十位_预测", "综合_个位_预测",
            "综合_完整号码",

            # 准确率统计列
            "简单分析_准确率", "马尔科夫_准确率", "ML_准确率", "综合_准确率",

            # 预测状态列
            "预测日期", "预测状态", "备注"
        ]

        for col in prediction_columns:
            if col not in df.columns:
                df[col] = None
    
    def _generate_predictions_for_new_data(self, combined_df, new_df):
        """为新数据生成预测结果"""
        if not self.analyzers:
            print("没有可用的分析器，跳过预测生成")
            return

        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print("开始为新数据生成预测结果...")

        # 为每个新期号生成预测
        for _, row in new_df.iterrows():
            issue = row['期号']

            # 找到在combined_df中的位置
            mask = combined_df['期号'] == issue
            if not mask.any():
                continue

            try:
                # 基于当前期号的前一期进行预测
                prev_issue = issue - 1
                prev_mask = combined_df['期号'] == prev_issue

                if prev_mask.any():
                    prev_row = combined_df[prev_mask].iloc[0]
                    prev_hundreds = int(prev_row['百位'])
                    prev_tens = int(prev_row['十位'])
                    prev_units = int(prev_row['个位'])

                    # 存储所有算法的预测结果
                    all_predictions = {}

                    # 生成简单分析预测
                    if 'simple' in self.analyzers:
                        simple_predictions = self._get_simple_predictions(
                            self.analyzers['simple'], prev_hundreds, prev_tens, prev_units
                        )
                        all_predictions['simple'] = simple_predictions

                        # 更新简单分析预测结果
                        combined_df.loc[mask, '简单_百位_预测'] = simple_predictions['hundreds']['digit']
                        combined_df.loc[mask, '简单_百位_概率'] = round(simple_predictions['hundreds']['prob'], 4)
                        combined_df.loc[mask, '简单_十位_预测'] = simple_predictions['tens']['digit']
                        combined_df.loc[mask, '简单_十位_概率'] = round(simple_predictions['tens']['prob'], 4)
                        combined_df.loc[mask, '简单_个位_预测'] = simple_predictions['units']['digit']
                        combined_df.loc[mask, '简单_个位_概率'] = round(simple_predictions['units']['prob'], 4)

                    # 生成马尔科夫链预测
                    if 'markov' in self.analyzers:
                        markov_predictions = self._get_markov_predictions(
                            self.analyzers['markov'], prev_hundreds, prev_tens, prev_units
                        )
                        all_predictions['markov'] = markov_predictions

                        # 更新马尔科夫预测结果
                        combined_df.loc[mask, '马尔科夫_百位_预测'] = markov_predictions['hundreds']['digit']
                        combined_df.loc[mask, '马尔科夫_百位_概率'] = round(markov_predictions['hundreds']['prob'], 4)
                        combined_df.loc[mask, '马尔科夫_十位_预测'] = markov_predictions['tens']['digit']
                        combined_df.loc[mask, '马尔科夫_十位_概率'] = round(markov_predictions['tens']['prob'], 4)
                        combined_df.loc[mask, '马尔科夫_个位_预测'] = markov_predictions['units']['digit']
                        combined_df.loc[mask, '马尔科夫_个位_概率'] = round(markov_predictions['units']['prob'], 4)

                    # 生成机器学习预测
                    if 'ml' in self.analyzers:
                        ml_predictions = self._get_ml_predictions(
                            self.analyzers['ml'], issue
                        )
                        all_predictions['ml'] = ml_predictions

                        # 更新机器学习预测结果
                        if ml_predictions:
                            for position in ['hundreds', 'tens', 'units']:
                                pos_name = {'hundreds': '百位', 'tens': '十位', 'units': '个位'}[position]
                                if position in ml_predictions:
                                    pred = ml_predictions[position]
                                    if 'naive_bayes' in pred:
                                        nb_best = max(pred['naive_bayes'].items(), key=lambda x: x[1])
                                        combined_df.loc[mask, f'ML_朴素贝叶斯_{pos_name}'] = nb_best[0]
                                    if 'decision_tree' in pred:
                                        dt_best = max(pred['decision_tree'].items(), key=lambda x: x[1])
                                        combined_df.loc[mask, f'ML_决策树_{pos_name}'] = dt_best[0]
                                    if 'neural_network' in pred:
                                        nn_best = max(pred['neural_network'].items(), key=lambda x: x[1])
                                        combined_df.loc[mask, f'ML_神经网络_{pos_name}'] = nn_best[0]

                    # 生成综合预测
                    comprehensive_prediction = self._generate_comprehensive_prediction(all_predictions)
                    if comprehensive_prediction:
                        combined_df.loc[mask, '综合_百位_预测'] = comprehensive_prediction['hundreds']
                        combined_df.loc[mask, '综合_十位_预测'] = comprehensive_prediction['tens']
                        combined_df.loc[mask, '综合_个位_预测'] = comprehensive_prediction['units']
                        combined_df.loc[mask, '综合_完整号码'] = f"{comprehensive_prediction['hundreds']}{comprehensive_prediction['tens']}{comprehensive_prediction['units']}"

                    # 设置预测状态
                    combined_df.loc[mask, '预测日期'] = current_time
                    combined_df.loc[mask, '预测状态'] = "已生成"

                    # 生成备注
                    used_algorithms = list(all_predictions.keys())
                    combined_df.loc[mask, '备注'] = f"使用算法: {', '.join(used_algorithms)}"

                    # 显示预测结果
                    if comprehensive_prediction:
                        print(f"期号 {issue}: 综合预测 {comprehensive_prediction['hundreds']}{comprehensive_prediction['tens']}{comprehensive_prediction['units']}")
                    elif 'simple' in all_predictions:
                        simple = all_predictions['simple']
                        print(f"期号 {issue}: 简单预测 {simple['hundreds']['digit']}{simple['tens']['digit']}{simple['units']['digit']}")

            except Exception as e:
                print(f"为期号 {issue} 生成预测时出错: {e}")
                combined_df.loc[mask, '预测状态'] = "预测失败"
                combined_df.loc[mask, '备注'] = f"错误: {str(e)}"
    
    def _get_simple_predictions(self, analyzer, hundreds, tens, units):
        """获取简单分析预测结果"""
        predictions = {}
        
        positions = {
            'hundreds': hundreds,
            'tens': tens, 
            'units': units
        }
        
        for position, current_number in positions.items():
            try:
                # 获取预测结果
                pred_results = analyzer.predict_next_numbers(position, current_number, top_k=1)
                if pred_results:
                    digit, prob = pred_results[0]
                    predictions[position] = {'digit': digit, 'prob': prob}
                else:
                    predictions[position] = {'digit': 0, 'prob': 0.1}
            except Exception as e:
                print(f"预测 {position} 位时出错: {e}")
                predictions[position] = {'digit': 0, 'prob': 0.1}
        
        return predictions

    def _get_markov_predictions(self, analyzer, hundreds, tens, units):
        """获取马尔科夫链预测结果"""
        predictions = {}

        positions = {
            'hundreds': hundreds,
            'tens': tens,
            'units': units
        }

        for position, current_number in positions.items():
            try:
                # 这里应该调用马尔科夫链分析器的预测方法
                # 由于模块可能不完整，使用简化版本
                predictions[position] = {'digit': (current_number + 1) % 10, 'prob': 0.15}
            except Exception as e:
                print(f"马尔科夫预测 {position} 位时出错: {e}")
                predictions[position] = {'digit': 0, 'prob': 0.1}

        return predictions

    def _get_ml_predictions(self, analyzer, issue):
        """获取机器学习预测结果"""
        try:
            # 提取特征
            features = analyzer.extract_features(window_size=10, target_issue=issue)

            if features:
                predictions = {}

                for position in ['hundreds', 'tens', 'units']:
                    # 朴素贝叶斯预测
                    nb_pred = analyzer.naive_bayes_classifier(position, features, target_issue=issue)

                    # 决策树预测
                    dt_pred = analyzer.decision_tree_classifier(position, features, target_issue=issue)

                    # 神经网络预测
                    nn_pred = analyzer.simple_neural_network(position, features, target_issue=issue)

                    predictions[position] = {
                        'naive_bayes': nb_pred,
                        'decision_tree': dt_pred,
                        'neural_network': nn_pred
                    }

                return predictions
            else:
                return None

        except Exception as e:
            print(f"机器学习预测时出错: {e}")
            return None

    def _generate_comprehensive_prediction(self, all_predictions):
        """生成综合预测结果"""
        if not all_predictions:
            return None

        comprehensive = {}

        for position in ['hundreds', 'tens', 'units']:
            votes = {}

            # 收集各算法的预测
            if 'simple' in all_predictions:
                digit = all_predictions['simple'][position]['digit']
                prob = all_predictions['simple'][position]['prob']
                votes[digit] = votes.get(digit, 0) + prob * 0.4  # 简单分析权重40%

            if 'markov' in all_predictions:
                digit = all_predictions['markov'][position]['digit']
                prob = all_predictions['markov'][position]['prob']
                votes[digit] = votes.get(digit, 0) + prob * 0.3  # 马尔科夫权重30%

            if 'ml' in all_predictions and all_predictions['ml']:
                ml_pred = all_predictions['ml'].get(position, {})
                if 'naive_bayes' in ml_pred:
                    nb_best = max(ml_pred['naive_bayes'].items(), key=lambda x: x[1])
                    votes[nb_best[0]] = votes.get(nb_best[0], 0) + nb_best[1] * 0.3  # ML权重30%

            # 选择得票最高的数字
            if votes:
                best_digit = max(votes.items(), key=lambda x: x[1])[0]
                comprehensive[position] = best_digit
            else:
                comprehensive[position] = 0

        return comprehensive
    
    def run_full_update(self, num_periods=5000):
        """运行完整更新流程"""
        print("="*60)
        print("3D彩票增强版爬虫和预测系统启动")
        print("="*60)
        
        # 获取最后一期数据
        last_period = None
        if os.path.exists(self.excel_file):
            try:
                existing_df = pd.read_excel(self.excel_file)
                if not existing_df.empty:
                    last_period = existing_df["期号"].max()
                    print(f"检测到最新期号: {last_period}")
            except Exception as e:
                print(f"读取现有数据失败: {e}")
        
        # 获取新数据
        lottery_data = self.get_lottery_data(last_period, num_periods)
        
        if lottery_data:
            # 保存数据并生成预测
            result_df = self.save_to_excel_with_predictions(lottery_data)
            
            # 计算准确率
            self._calculate_accuracy(result_df)
            
            print("\n" + "="*60)
            print("更新完成！")
            print("="*60)
            
            return result_df
        else:
            print("没有新数据需要更新")
            return None
    
    def _calculate_accuracy(self, df):
        """计算预测准确率"""
        print("\n计算预测准确率...")

        try:
            # 找到有预测结果的行
            has_prediction = df['简单_百位_预测'].notna()
            prediction_rows = df[has_prediction].copy()

            if len(prediction_rows) == 0:
                print("没有找到预测数据")
                return

            # 计算简单分析准确率
            correct_hundreds = (prediction_rows['简单_百位_预测'] == prediction_rows['百位']).sum()
            correct_tens = (prediction_rows['简单_十位_预测'] == prediction_rows['十位']).sum()
            correct_units = (prediction_rows['简单_个位_预测'] == prediction_rows['个位']).sum()
            correct_complete = (
                (prediction_rows['简单_百位_预测'] == prediction_rows['百位']) &
                (prediction_rows['简单_十位_预测'] == prediction_rows['十位']) &
                (prediction_rows['简单_个位_预测'] == prediction_rows['个位'])
            ).sum()

            total_predictions = len(prediction_rows)

            # 计算准确率
            hundreds_accuracy = (correct_hundreds / total_predictions) * 100
            tens_accuracy = (correct_tens / total_predictions) * 100
            units_accuracy = (correct_units / total_predictions) * 100
            complete_accuracy = (correct_complete / total_predictions) * 100

            # 更新准确率列
            accuracy_text = f"百位:{hundreds_accuracy:.1f}% 十位:{tens_accuracy:.1f}% 个位:{units_accuracy:.1f}% 完整:{complete_accuracy:.1f}%"
            df.loc[has_prediction, '简单分析_准确率'] = accuracy_text

            print(f"简单分析准确率统计 (基于{total_predictions}期预测):")
            print(f"  百位准确率: {hundreds_accuracy:.2f}% ({correct_hundreds}/{total_predictions})")
            print(f"  十位准确率: {tens_accuracy:.2f}% ({correct_tens}/{total_predictions})")
            print(f"  个位准确率: {units_accuracy:.2f}% ({correct_units}/{total_predictions})")
            print(f"  完整号码准确率: {complete_accuracy:.2f}% ({correct_complete}/{total_predictions})")

            # 保存更新后的数据
            df.to_excel(self.excel_file, index=False)

        except Exception as e:
            print(f"计算准确率时出错: {e}")

    def generate_prediction_report(self, df):
        """生成预测报告"""
        print("\n" + "="*50)
        print("预测结果报告")
        print("="*50)

        try:
            # 获取最新的预测
            latest_predictions = df[df['预测状态'] == '已生成'].head(10)

            if len(latest_predictions) > 0:
                print("\n最新10期预测结果:")
                print("-" * 80)
                print(f"{'期号':<10} {'实际号码':<10} {'预测号码':<10} {'百位':<6} {'十位':<6} {'个位':<6}")
                print("-" * 80)

                for _, row in latest_predictions.iterrows():
                    issue = int(row['期号'])
                    actual = f"{int(row['百位'])}{int(row['十位'])}{int(row['个位'])}"
                    predicted = row['综合_完整号码'] if pd.notna(row['综合_完整号码']) else "---"

                    # 检查各位预测准确性
                    h_mark = "✓" if pd.notna(row['简单_百位_预测']) and int(row['简单_百位_预测']) == int(row['百位']) else "✗"
                    t_mark = "✓" if pd.notna(row['简单_十位_预测']) and int(row['简单_十位_预测']) == int(row['十位']) else "✗"
                    u_mark = "✓" if pd.notna(row['简单_个位_预测']) and int(row['简单_个位_预测']) == int(row['个位']) else "✗"

                    print(f"{issue:<10} {actual:<10} {predicted:<10} {h_mark:<6} {t_mark:<6} {u_mark:<6}")

            # 显示算法表现统计
            self._show_algorithm_performance(df)

        except Exception as e:
            print(f"生成预测报告时出错: {e}")

    def _show_algorithm_performance(self, df):
        """显示算法表现统计"""
        print("\n算法表现统计:")
        print("-" * 40)

        try:
            has_prediction = df['简单_百位_预测'].notna()
            if has_prediction.sum() > 0:
                recent_data = df[has_prediction].head(50)  # 最近50期

                if len(recent_data) > 0:
                    # 计算最近表现
                    recent_correct = (
                        (recent_data['简单_百位_预测'] == recent_data['百位']) &
                        (recent_data['简单_十位_预测'] == recent_data['十位']) &
                        (recent_data['简单_个位_预测'] == recent_data['个位'])
                    ).sum()

                    recent_accuracy = (recent_correct / len(recent_data)) * 100
                    print(f"简单分析算法 (最近{len(recent_data)}期): {recent_accuracy:.2f}%")

                    # 分析趋势
                    if len(recent_data) >= 20:
                        first_half = recent_data.tail(25)  # 较早的25期
                        second_half = recent_data.head(25)  # 较新的25期

                        first_correct = (
                            (first_half['简单_百位_预测'] == first_half['百位']) &
                            (first_half['简单_十位_预测'] == first_half['十位']) &
                            (first_half['简单_个位_预测'] == first_half['个位'])
                        ).sum()

                        second_correct = (
                            (second_half['简单_百位_预测'] == second_half['百位']) &
                            (second_half['简单_十位_预测'] == second_half['十位']) &
                            (second_half['简单_个位_预测'] == second_half['个位'])
                        ).sum()

                        first_acc = (first_correct / len(first_half)) * 100
                        second_acc = (second_correct / len(second_half)) * 100

                        trend = "上升" if second_acc > first_acc else "下降" if second_acc < first_acc else "稳定"
                        print(f"  趋势: {trend} (早期{first_acc:.1f}% → 近期{second_acc:.1f}%)")

        except Exception as e:
            print(f"显示算法表现时出错: {e}")

def main():
    """主函数"""
    print("3D彩票增强版爬虫和预测系统")
    print("功能: 数据爬取 + 多算法预测 + Excel整合 + 横向对比")
    print("="*60)

    spider = Enhanced3DSpider()
    result_df = spider.run_full_update()

    if result_df is not None:
        spider.generate_prediction_report(result_df)

    print("\n系统运行完成！")

if __name__ == "__main__":
    main()
