# 贪吃蛇游戏

一个界面简洁美观的贪吃蛇游戏，使用 Python 和 Pygame 开发。

## 功能特点

- 🎨 **现代化UI设计**：采用现代配色方案，圆角矩形设计
- 🎮 **经典玩法**：传统贪吃蛇游戏机制
- 📊 **计分系统**：实时显示分数和速度
- ⏸️ **暂停功能**：支持游戏暂停和继续
- 🚀 **速度递增**：随着分数增加，游戏速度逐渐提升
- 🔄 **重新开始**：游戏结束后可以重新开始或返回主菜单

## 游戏截图

游戏界面包含：
- 主菜单界面
- 游戏进行界面
- 游戏暂停界面
- 游戏结束界面

## 安装要求

- Python 3.6+
- Pygame 库

### 安装 Pygame

```bash
pip install pygame
```

## 运行游戏

1. 确保已安装 Python 和 Pygame
2. 下载游戏文件到本地
3. 在终端中进入游戏目录
4. 运行游戏：

```bash
python snake_game.py
```

## 游戏操作

### 主菜单
- 点击"开始游戏"按钮开始游戏
- 点击"退出游戏"按钮退出程序

### 游戏中
- **方向键**：控制蛇的移动方向
  - ↑：向上移动
  - ↓：向下移动
  - ←：向左移动
  - →：向右移动
- **空格键**：暂停/继续游戏

### 游戏结束
- 点击"重新开始"按钮重新开始游戏
- 点击"返回主菜单"按钮返回主菜单

## 游戏规则

1. 控制蛇移动，吃掉红色的食物
2. 每吃一个食物，蛇身会增长，分数增加10分
3. 游戏速度会随着分数增加而提升
4. 避免撞到墙壁或蛇自己的身体
5. 撞到墙壁或自己身体时游戏结束

## 配置说明

游戏配置在 `config.py` 文件中，可以自定义：

- 窗口大小和游戏区域大小
- 颜色方案
- 游戏速度设置
- 字体大小
- 网格大小

## 文件结构

```
snake_game/
├── snake_game.py    # 主游戏文件
├── config.py        # 游戏配置文件
└── README.md        # 说明文档
```

## 技术特点

- 使用面向对象编程设计
- 模块化配置管理
- 现代化UI设计理念
- 流畅的游戏体验
- 完善的错误处理

## 开发者

这个游戏由 Augment Agent 开发，基于 Python 和 Pygame 库。

## 许可证

本项目仅供学习和娱乐使用。
