# 贪吃蛇游戏配置文件
import pygame

# 游戏窗口设置
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
GAME_AREA_WIDTH = 600
GAME_AREA_HEIGHT = 480
GRID_SIZE = 20

# 颜色配置 (现代化配色方案)
COLORS = {
    'background': (26, 32, 44),      # 深蓝灰色背景
    'game_area': (45, 55, 72),       # 游戏区域背景
    'snake_head': (72, 187, 120),    # 蛇头 - 绿色
    'snake_body': (104, 211, 145),   # 蛇身 - 浅绿色
    'food': (245, 101, 101),         # 食物 - 红色
    'text_primary': (247, 250, 252), # 主要文字 - 白色
    'text_secondary': (160, 174, 192), # 次要文字 - 灰色
    'border': (74, 85, 104),         # 边框颜色
    'button': (66, 153, 225),        # 按钮颜色
    'button_hover': (90, 103, 216),  # 按钮悬停颜色
}

# 游戏设置
INITIAL_SPEED = 8  # 初始速度 (FPS)
SPEED_INCREMENT = 0.5  # 每吃一个食物增加的速度
MAX_SPEED = 20  # 最大速度

# 字体设置
FONT_SIZES = {
    'title': 36,
    'score': 24,
    'button': 20,
    'small': 16
}

# 游戏区域位置
GAME_AREA_X = (WINDOW_WIDTH - GAME_AREA_WIDTH) // 2
GAME_AREA_Y = 80

# 按钮设置
BUTTON_WIDTH = 120
BUTTON_HEIGHT = 40
BUTTON_RADIUS = 8

# 方向常量
DIRECTIONS = {
    'UP': (0, -1),
    'DOWN': (0, 1),
    'LEFT': (-1, 0),
    'RIGHT': (1, 0)
}

# 游戏状态
GAME_STATES = {
    'MENU': 'menu',
    'PLAYING': 'playing',
    'PAUSED': 'paused',
    'GAME_OVER': 'game_over'
}
