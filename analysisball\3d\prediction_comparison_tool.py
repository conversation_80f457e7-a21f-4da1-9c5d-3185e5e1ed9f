#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D彩票预测结果横向对比分析工具
功能：
1. 对比不同算法的预测准确率
2. 分析预测趋势和模式
3. 生成对比报告
4. 推荐最佳算法组合
"""

import pandas as pd
import os
from datetime import datetime
import json

class PredictionComparisonTool:
    """预测结果对比分析工具"""
    
    def __init__(self, excel_file="analysisball/3d/3d_with_predictions.xlsx"):
        self.excel_file = excel_file
        self.df = None
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        if os.path.exists(self.excel_file):
            self.df = pd.read_excel(self.excel_file)
            print(f"成功加载 {len(self.df)} 期数据")
        else:
            print(f"文件不存在: {self.excel_file}")
    
    def analyze_algorithm_performance(self):
        """分析各算法表现"""
        if self.df is None:
            return
        
        print("\n" + "="*60)
        print("算法表现分析")
        print("="*60)
        
        # 筛选有预测数据的行
        has_prediction = self.df['简单_百位_预测'].notna()
        prediction_data = self.df[has_prediction].copy()
        
        if len(prediction_data) == 0:
            print("没有找到预测数据")
            return
        
        total_predictions = len(prediction_data)
        print(f"分析期数: {total_predictions}")
        
        # 分析简单分析算法
        self._analyze_simple_algorithm(prediction_data, total_predictions)
        
        # 分析机器学习算法
        self._analyze_ml_algorithm(prediction_data, total_predictions)
        
        # 分析综合预测
        self._analyze_comprehensive_prediction(prediction_data, total_predictions)
    
    def _analyze_simple_algorithm(self, data, total):
        """分析简单分析算法"""
        print("\n1. 简单分析算法表现:")
        print("-" * 40)
        
        # 各位准确率
        h_correct = (data['简单_百位_预测'] == data['百位']).sum()
        t_correct = (data['简单_十位_预测'] == data['十位']).sum()
        u_correct = (data['简单_个位_预测'] == data['个位']).sum()
        complete_correct = (
            (data['简单_百位_预测'] == data['百位']) &
            (data['简单_十位_预测'] == data['十位']) &
            (data['简单_个位_预测'] == data['个位'])
        ).sum()
        
        print(f"  百位准确率: {h_correct/total*100:.2f}% ({h_correct}/{total})")
        print(f"  十位准确率: {t_correct/total*100:.2f}% ({t_correct}/{total})")
        print(f"  个位准确率: {u_correct/total*100:.2f}% ({u_correct}/{total})")
        print(f"  完整号码准确率: {complete_correct/total*100:.2f}% ({complete_correct}/{total})")
        
        # 分析概率分布
        avg_h_prob = data['简单_百位_概率'].mean()
        avg_t_prob = data['简单_十位_概率'].mean()
        avg_u_prob = data['简单_个位_概率'].mean()
        
        print(f"  平均预测概率: 百位{avg_h_prob:.3f}, 十位{avg_t_prob:.3f}, 个位{avg_u_prob:.3f}")
    
    def _analyze_ml_algorithm(self, data, total):
        """分析机器学习算法"""
        print("\n2. 机器学习算法表现:")
        print("-" * 40)
        
        # 检查是否有ML预测数据
        has_ml = data['ML_朴素贝叶斯_百位'].notna()
        ml_data = data[has_ml]
        
        if len(ml_data) == 0:
            print("  没有机器学习预测数据")
            return
        
        ml_total = len(ml_data)
        print(f"  ML预测期数: {ml_total}")
        
        # 朴素贝叶斯准确率
        nb_h_correct = (ml_data['ML_朴素贝叶斯_百位'] == ml_data['百位']).sum()
        nb_t_correct = (ml_data['ML_朴素贝叶斯_十位'] == ml_data['十位']).sum()
        nb_u_correct = (ml_data['ML_朴素贝叶斯_个位'] == ml_data['个位']).sum()
        
        print(f"  朴素贝叶斯 - 百位: {nb_h_correct/ml_total*100:.2f}% ({nb_h_correct}/{ml_total})")
        print(f"  朴素贝叶斯 - 十位: {nb_t_correct/ml_total*100:.2f}% ({nb_t_correct}/{ml_total})")
        print(f"  朴素贝叶斯 - 个位: {nb_u_correct/ml_total*100:.2f}% ({nb_u_correct}/{ml_total})")
    
    def _analyze_comprehensive_prediction(self, data, total):
        """分析综合预测"""
        print("\n3. 综合预测表现:")
        print("-" * 40)
        
        # 综合预测准确率
        comp_h_correct = (data['综合_百位_预测'] == data['百位']).sum()
        comp_t_correct = (data['综合_十位_预测'] == data['十位']).sum()
        comp_u_correct = (data['综合_个位_预测'] == data['个位']).sum()
        comp_complete_correct = (
            (data['综合_百位_预测'] == data['百位']) &
            (data['综合_十位_预测'] == data['十位']) &
            (data['综合_个位_预测'] == data['个位'])
        ).sum()
        
        print(f"  百位准确率: {comp_h_correct/total*100:.2f}% ({comp_h_correct}/{total})")
        print(f"  十位准确率: {comp_t_correct/total*100:.2f}% ({comp_t_correct}/{total})")
        print(f"  个位准确率: {comp_u_correct/total*100:.2f}% ({comp_u_correct}/{total})")
        print(f"  完整号码准确率: {comp_complete_correct/total*100:.2f}% ({comp_complete_correct}/{total})")
    
    def analyze_prediction_trends(self):
        """分析预测趋势"""
        if self.df is None:
            return
        
        print("\n" + "="*60)
        print("预测趋势分析")
        print("="*60)
        
        has_prediction = self.df['简单_百位_预测'].notna()
        prediction_data = self.df[has_prediction].copy()
        
        if len(prediction_data) < 6:
            print("数据不足，无法进行趋势分析")
            return
        
        # 分析最近趋势（前半部分 vs 后半部分）
        mid_point = len(prediction_data) // 2
        recent_data = prediction_data.head(mid_point)  # 最新的一半
        earlier_data = prediction_data.tail(mid_point)  # 较早的一半
        
        print(f"对比分析: 最近{len(recent_data)}期 vs 较早{len(earlier_data)}期")
        
        # 简单分析趋势
        self._analyze_trend_for_algorithm(recent_data, earlier_data, "简单分析", 
                                        ['简单_百位_预测', '简单_十位_预测', '简单_个位_预测'])
        
        # 综合预测趋势
        self._analyze_trend_for_algorithm(recent_data, earlier_data, "综合预测",
                                        ['综合_百位_预测', '综合_十位_预测', '综合_个位_预测'])
    
    def _analyze_trend_for_algorithm(self, recent_data, earlier_data, algorithm_name, columns):
        """分析特定算法的趋势"""
        print(f"\n{algorithm_name}趋势:")
        print("-" * 30)
        
        positions = ['百位', '十位', '个位']
        actual_columns = ['百位', '十位', '个位']
        
        for i, (pred_col, actual_col, pos_name) in enumerate(zip(columns, actual_columns, positions)):
            # 计算最近期准确率
            recent_correct = (recent_data[pred_col] == recent_data[actual_col]).sum()
            recent_accuracy = recent_correct / len(recent_data) * 100
            
            # 计算较早期准确率
            earlier_correct = (earlier_data[pred_col] == earlier_data[actual_col]).sum()
            earlier_accuracy = earlier_correct / len(earlier_data) * 100
            
            # 趋势判断
            if recent_accuracy > earlier_accuracy + 5:
                trend = "上升"
            elif recent_accuracy < earlier_accuracy - 5:
                trend = "下降"
            else:
                trend = "稳定"
            
            print(f"  {pos_name}: {earlier_accuracy:.1f}% → {recent_accuracy:.1f}% ({trend})")
    
    def generate_comparison_report(self):
        """生成对比报告"""
        if self.df is None:
            return
        
        print("\n" + "="*60)
        print("预测对比报告")
        print("="*60)
        
        has_prediction = self.df['简单_百位_预测'].notna()
        prediction_data = self.df[has_prediction].copy()
        
        if len(prediction_data) == 0:
            print("没有预测数据可供分析")
            return
        
        # 生成详细对比表
        print(f"\n最近{min(10, len(prediction_data))}期预测对比:")
        print("-" * 100)
        print(f"{'期号':<8} {'实际':<6} {'简单预测':<8} {'综合预测':<8} {'简单准确':<8} {'综合准确':<8}")
        print("-" * 100)
        
        for _, row in prediction_data.head(10).iterrows():
            issue = int(row['期号'])
            actual = f"{int(row['百位'])}{int(row['十位'])}{int(row['个位'])}"
            
            simple_pred = "---"
            if pd.notna(row['简单_百位_预测']):
                simple_pred = f"{int(row['简单_百位_预测'])}{int(row['简单_十位_预测'])}{int(row['简单_个位_预测'])}"
            
            comp_pred = row['综合_完整号码'] if pd.notna(row['综合_完整号码']) else "---"
            
            # 检查准确性
            simple_accurate = "Y" if simple_pred == actual else "N"
            comp_accurate = "Y" if comp_pred == actual else "N"
            
            print(f"{issue:<8} {actual:<6} {simple_pred:<8} {comp_pred:<8} {simple_accurate:<8} {comp_accurate:<8}")
    
    def recommend_best_strategy(self):
        """推荐最佳策略"""
        if self.df is None:
            return
        
        print("\n" + "="*60)
        print("最佳策略推荐")
        print("="*60)
        
        has_prediction = self.df['简单_百位_预测'].notna()
        prediction_data = self.df[has_prediction].copy()
        
        if len(prediction_data) == 0:
            print("没有预测数据可供分析")
            return
        
        total = len(prediction_data)
        
        # 计算各算法各位的准确率
        algorithms = {
            '简单分析': {
                'columns': ['简单_百位_预测', '简单_十位_预测', '简单_个位_预测'],
                'scores': []
            },
            '综合预测': {
                'columns': ['综合_百位_预测', '综合_十位_预测', '综合_个位_预测'],
                'scores': []
            }
        }
        
        actual_columns = ['百位', '十位', '个位']
        positions = ['百位', '十位', '个位']
        
        for alg_name, alg_info in algorithms.items():
            for pred_col, actual_col, pos_name in zip(alg_info['columns'], actual_columns, positions):
                correct = (prediction_data[pred_col] == prediction_data[actual_col]).sum()
                accuracy = correct / total * 100
                alg_info['scores'].append(accuracy)
        
        # 推荐策略
        print("各位最佳算法推荐:")
        for i, pos_name in enumerate(positions):
            best_alg = None
            best_score = 0
            
            for alg_name, alg_info in algorithms.items():
                if alg_info['scores'][i] > best_score:
                    best_score = alg_info['scores'][i]
                    best_alg = alg_name
            
            print(f"  {pos_name}: {best_alg} (准确率: {best_score:.2f}%)")
        
        # 整体推荐
        simple_avg = sum(algorithms['简单分析']['scores']) / 3
        comp_avg = sum(algorithms['综合预测']['scores']) / 3
        
        print(f"\n整体表现:")
        print(f"  简单分析平均准确率: {simple_avg:.2f}%")
        print(f"  综合预测平均准确率: {comp_avg:.2f}%")
        
        if comp_avg > simple_avg:
            print(f"\n推荐使用: 综合预测 (优势: {comp_avg - simple_avg:.2f}%)")
        else:
            print(f"\n推荐使用: 简单分析 (优势: {simple_avg - comp_avg:.2f}%)")
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("3D彩票预测结果横向对比分析")
        print("="*60)
        
        if self.df is None:
            print("无法加载数据，分析终止")
            return
        
        # 运行各项分析
        self.analyze_algorithm_performance()
        self.analyze_prediction_trends()
        self.generate_comparison_report()
        self.recommend_best_strategy()
        
        print("\n" + "="*60)
        print("分析完成！")
        print("="*60)

def main():
    """主函数"""
    tool = PredictionComparisonTool()
    tool.run_full_analysis()

if __name__ == "__main__":
    main()
