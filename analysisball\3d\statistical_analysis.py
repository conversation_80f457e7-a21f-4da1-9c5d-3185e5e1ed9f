#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D彩票统计分析模块
功能：
1. 频率分析 - 各数字出现频率统计
2. 分布分析 - 数字分布的统计特征
3. 相关性分析 - 百位、十位、个位之间的相关性
4. 热度分析 - 冷热号码分析
5. 间隔分析 - 数字出现间隔统计
6. 组合分析 - 数字组合模式分析
"""

import numpy as np
import pandas as pd
import sqlite3
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from typing import Dict, List, Tuple, Optional
from pathlib import Path
import logging
from collections import Counter, defaultdict

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class Statistical3DAnalyzer:
    """3D彩票统计分析类"""

    def __init__(self, data_source: str = "analysisball/3d/3d_data.db"):
        """
        初始化统计分析器

        Args:
            data_source: 数据源路径
        """
        self.data_source = Path(data_source)
        self.data = None

    def load_data(self) -> pd.DataFrame:
        """加载3D彩票数据"""
        try:
            if self.data_source.suffix == '.db':
                with sqlite3.connect(self.data_source) as conn:
                    query = '''
                        SELECT period, date, hundreds, tens, units, number_str
                        FROM lottery_3d
                        ORDER BY period ASC
                    '''
                    self.data = pd.read_sql_query(query, conn)
            elif self.data_source.suffix in ['.xlsx', '.xls']:
                self.data = pd.read_excel(self.data_source)
                if '期号' in self.data.columns:
                    self.data = self.data.rename(columns={
                        '期号': 'period', '日期': 'date',
                        '百位': 'hundreds', '十位': 'tens', '个位': 'units'
                    })

            # 确保数据类型正确
            self.data['hundreds'] = self.data['hundreds'].astype(int)
            self.data['tens'] = self.data['tens'].astype(int)
            self.data['units'] = self.data['units'].astype(int)

            logger.info(f"成功加载 {len(self.data)} 条3D彩票数据")
            return self.data

        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            raise

    def frequency_analysis(self) -> Dict[str, Dict]:
        """
        频率分析 - 统计各位置数字的出现频率

        Returns:
            各位置的频率统计结果
        """
        if self.data is None:
            self.load_data()

        positions = ['hundreds', 'tens', 'units']
        position_names = ['百位', '十位', '个位']

        results = {}

        for pos, pos_name in zip(positions, position_names):
            # 计算频率
            frequency = self.data[pos].value_counts().sort_index()
            total_count = len(self.data)

            # 计算概率
            probability = frequency / total_count

            # 理论概率（均匀分布）
            theoretical_prob = 1.0 / 10

            # 计算偏差
            deviation = probability - theoretical_prob

            # 卡方检验
            expected = [total_count * theoretical_prob] * 10
            observed = [frequency.get(i, 0) for i in range(10)]
            chi2_stat, p_value = stats.chisquare(observed, expected)

            results[pos] = {
                'position_name': pos_name,
                'frequency': frequency.to_dict(),
                'probability': probability.to_dict(),
                'deviation_from_uniform': deviation.to_dict(),
                'chi2_test': {
                    'statistic': chi2_stat,
                    'p_value': p_value,
                    'is_uniform': p_value > 0.05
                },
                'statistics': {
                    'most_frequent': frequency.idxmax(),
                    'least_frequent': frequency.idxmin(),
                    'max_frequency': frequency.max(),
                    'min_frequency': frequency.min(),
                    'variance': probability.var(),
                    'entropy': -np.sum(probability * np.log2(probability + 1e-10))
                }
            }

        return results

    def correlation_analysis(self) -> Dict[str, any]:
        """
        相关性分析 - 分析百位、十位、个位之间的相关性

        Returns:
            相关性分析结果
        """
        if self.data is None:
            self.load_data()

        # 计算皮尔逊相关系数
        positions_data = self.data[['hundreds', 'tens', 'units']]
        correlation_matrix = positions_data.corr()

        # 计算斯皮尔曼相关系数
        spearman_corr = positions_data.corr(method='spearman')

        # 计算肯德尔相关系数
        kendall_corr = positions_data.corr(method='kendall')

        # 独立性检验（卡方检验）
        independence_tests = {}

        pairs = [('hundreds', 'tens'), ('hundreds', 'units'), ('tens', 'units')]
        for pos1, pos2 in pairs:
            # 创建列联表
            contingency_table = pd.crosstab(self.data[pos1], self.data[pos2])

            # 卡方检验
            chi2, p_value, dof, expected = stats.chi2_contingency(contingency_table)

            independence_tests[f"{pos1}_{pos2}"] = {
                'chi2_statistic': chi2,
                'p_value': p_value,
                'degrees_of_freedom': dof,
                'is_independent': p_value > 0.05
            }

        return {
            'pearson_correlation': correlation_matrix.to_dict(),
            'spearman_correlation': spearman_corr.to_dict(),
            'kendall_correlation': kendall_corr.to_dict(),
            'independence_tests': independence_tests
        }

    def hot_cold_analysis(self, recent_periods: int = 100) -> Dict[str, Dict]:
        """
        冷热号码分析

        Args:
            recent_periods: 最近期数，用于分析近期冷热情况

        Returns:
            冷热分析结果
        """
        if self.data is None:
            self.load_data()

        positions = ['hundreds', 'tens', 'units']
        position_names = ['百位', '十位', '个位']

        results = {}

        for pos, pos_name in zip(positions, position_names):
            # 全期统计
            all_frequency = self.data[pos].value_counts()

            # 最近期统计
            recent_data = self.data.tail(recent_periods)
            recent_frequency = recent_data[pos].value_counts()

            # 计算冷热指数（最近期频率 / 全期平均频率）
            avg_frequency = len(self.data) / 10  # 理论平均频率
            recent_avg = recent_periods / 10     # 最近期理论平均频率

            hot_cold_index = {}
            for digit in range(10):
                recent_count = recent_frequency.get(digit, 0)
                hot_cold_index[digit] = recent_count / recent_avg if recent_avg > 0 else 0

            # 分类冷热号码
            hot_numbers = [d for d, idx in hot_cold_index.items() if idx > 1.2]
            warm_numbers = [d for d, idx in hot_cold_index.items() if 0.8 <= idx <= 1.2]
            cold_numbers = [d for d, idx in hot_cold_index.items() if idx < 0.8]

            results[pos] = {
                'position_name': pos_name,
                'all_period_frequency': all_frequency.to_dict(),
                'recent_period_frequency': recent_frequency.to_dict(),
                'hot_cold_index': hot_cold_index,
                'hot_numbers': hot_numbers,
                'warm_numbers': warm_numbers,
                'cold_numbers': cold_numbers,
                'analysis_periods': {
                    'total': len(self.data),
                    'recent': recent_periods
                }
            }

        return results

    def interval_analysis(self) -> Dict[str, Dict]:
        """
        间隔分析 - 分析数字出现的间隔规律

        Returns:
            间隔分析结果
        """
        if self.data is None:
            self.load_data()

        positions = ['hundreds', 'tens', 'units']
        position_names = ['百位', '十位', '个位']

        results = {}

        for pos, pos_name in zip(positions, position_names):
            sequence = self.data[pos].values

            # 计算每个数字的出现间隔
            digit_intervals = defaultdict(list)
            last_positions = {}

            for i, digit in enumerate(sequence):
                if digit in last_positions:
                    interval = i - last_positions[digit]
                    digit_intervals[digit].append(interval)
                last_positions[digit] = i

            # 统计间隔信息
            interval_stats = {}
            for digit in range(10):
                intervals = digit_intervals[digit]
                if intervals:
                    interval_stats[digit] = {
                        'count': len(intervals),
                        'min_interval': min(intervals),
                        'max_interval': max(intervals),
                        'avg_interval': sum(intervals) / len(intervals),
                        'current_interval': len(sequence) - last_positions.get(digit, 0) - 1
                    }
                else:
                    interval_stats[digit] = {
                        'count': 0,
                        'min_interval': 0,
                        'max_interval': 0,
                        'avg_interval': 0,
                        'current_interval': len(sequence)
                    }

            results[pos] = {
                'position_name': pos_name,
                'interval_statistics': interval_stats
            }

        return results

    def combination_analysis(self) -> Dict[str, any]:
        """
        组合分析 - 分析三位数字的组合模式

        Returns:
            组合分析结果
        """
        if self.data is None:
            self.load_data()

        # 分析和值（三位数字之和）
        self.data['sum'] = self.data['hundreds'] + self.data['tens'] + self.data['units']
        sum_frequency = self.data['sum'].value_counts().sort_index()

        # 分析跨度（最大数字 - 最小数字）
        self.data['span'] = self.data[['hundreds', 'tens', 'units']].max(axis=1) - \
                           self.data[['hundreds', 'tens', 'units']].min(axis=1)
        span_frequency = self.data['span'].value_counts().sort_index()

        # 分析AC值（算术复杂性）
        ac_values = []
        for _, row in self.data.iterrows():
            digits = [row['hundreds'], row['tens'], row['units']]
            differences = []
            for i in range(len(digits)):
                for j in range(i + 1, len(digits)):
                    differences.append(abs(digits[i] - digits[j]))
            ac_values.append(len(set(differences)) - 1)

        self.data['ac_value'] = ac_values
        ac_frequency = pd.Series(ac_values).value_counts().sort_index()

        # 分析质合比（质数和合数的比例）
        prime_numbers = {2, 3, 5, 7}
        prime_composite_patterns = []

        for _, row in self.data.iterrows():
            pattern = ''
            for digit in [row['hundreds'], row['tens'], row['units']]:
                if digit in prime_numbers:
                    pattern += '质'
                elif digit == 0 or digit == 1:
                    pattern += '特'  # 0和1特殊处理
                else:
                    pattern += '合'
            prime_composite_patterns.append(pattern)

        prime_composite_frequency = Counter(prime_composite_patterns)

        return {
            'sum_analysis': {
                'frequency': sum_frequency.to_dict(),
                'statistics': {
                    'min_sum': sum_frequency.index.min(),
                    'max_sum': sum_frequency.index.max(),
                    'most_frequent_sum': sum_frequency.idxmax(),
                    'avg_sum': self.data['sum'].mean()
                }
            },
            'span_analysis': {
                'frequency': span_frequency.to_dict(),
                'statistics': {
                    'min_span': span_frequency.index.min(),
                    'max_span': span_frequency.index.max(),
                    'most_frequent_span': span_frequency.idxmax(),
                    'avg_span': self.data['span'].mean()
                }
            },
            'ac_value_analysis': {
                'frequency': ac_frequency.to_dict(),
                'statistics': {
                    'min_ac': ac_frequency.index.min(),
                    'max_ac': ac_frequency.index.max(),
                    'most_frequent_ac': ac_frequency.idxmax(),
                    'avg_ac': np.mean(ac_values)
                }
            },
            'prime_composite_analysis': {
                'frequency': dict(prime_composite_frequency),
                'most_frequent_pattern': prime_composite_frequency.most_common(1)[0]
            }
        }