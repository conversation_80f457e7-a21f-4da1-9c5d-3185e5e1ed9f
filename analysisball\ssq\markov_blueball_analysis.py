#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球蓝球马尔科夫链分析和预测
使用马尔科夫链模型分析历史数据并预测下一个最可能出现的蓝球号码
"""

import numpy as np
import json
from collections import defaultdict, Counter
from datetime import datetime


class MarkovBlueBallAnalyzer:
    """马尔科夫链蓝球分析器"""
    
    def __init__(self, data_file_path):
        """
        初始化分析器
        
        Args:
            data_file_path (str): 数据文件路径
        """
        self.data_file_path = data_file_path
        self.raw_data = []
        self.time_ordered_data = []
        self.transition_matrix = {}
        self.transition_probabilities = {}
        self.states = set()
        
    def load_data(self):
        """加载并处理数据"""
        print("正在加载数据...")
        
        with open(self.data_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 提取数字数据
        self.raw_data = []
        for line in lines:
            line = line.strip()
            if line.isdigit():
                self.raw_data.append(int(line))
        
        # 反转数据，使其按时间顺序排列（最早的在前面）
        self.time_ordered_data = list(reversed(self.raw_data))
        
        # 获取所有可能的状态（1-16）
        self.states = set(self.time_ordered_data)
        
        print(f"数据加载完成，共 {len(self.time_ordered_data)} 条记录")
        print(f"数据范围: {min(self.states)} - {max(self.states)}")
        print(f"最新数据（原文件第一行）: {self.raw_data[0]}")
        
    def build_transition_matrix(self):
        """构建状态转移矩阵"""
        print("正在构建马尔科夫链转移矩阵...")
        
        # 初始化转移计数矩阵
        transition_counts = defaultdict(lambda: defaultdict(int))
        
        # 统计状态转移
        for i in range(len(self.time_ordered_data) - 1):
            current_state = self.time_ordered_data[i]
            next_state = self.time_ordered_data[i + 1]
            transition_counts[current_state][next_state] += 1
        
        # 计算转移概率
        self.transition_probabilities = {}
        for current_state in self.states:
            total_transitions = sum(transition_counts[current_state].values())
            if total_transitions > 0:
                self.transition_probabilities[current_state] = {}
                for next_state in self.states:
                    count = transition_counts[current_state][next_state]
                    probability = count / total_transitions
                    self.transition_probabilities[current_state][next_state] = probability
            else:
                # 如果某个状态没有转移记录，使用均匀分布
                self.transition_probabilities[current_state] = {
                    state: 1.0 / len(self.states) for state in self.states
                }
        
        self.transition_matrix = transition_counts
        print("转移矩阵构建完成")
        
    def analyze_transition_patterns(self):
        """分析转移模式"""
        print("\n=== 转移模式分析 ===")
        
        # 分析每个状态的转移偏好
        for state in sorted(self.states):
            if state in self.transition_probabilities:
                probs = self.transition_probabilities[state]
                # 找出概率最高的3个转移
                top_transitions = sorted(probs.items(), key=lambda x: x[1], reverse=True)[:3]
                
                print(f"状态 {state} 的主要转移:")
                for next_state, prob in top_transitions:
                    if prob > 0:
                        print(f"  -> {next_state}: {prob:.3f} ({prob*100:.1f}%)")
        
    def predict_next_value(self, current_value):
        """
        基于当前值预测下一个最可能的值
        
        Args:
            current_value (int): 当前值
            
        Returns:
            tuple: (预测值, 概率, 所有可能值的概率分布)
        """
        if current_value not in self.transition_probabilities:
            print(f"警告: 状态 {current_value} 在历史数据中没有转移记录")
            # 使用全局频率分布作为备选
            global_freq = Counter(self.time_ordered_data)
            total = len(self.time_ordered_data)
            probabilities = {state: count/total for state, count in global_freq.items()}
        else:
            probabilities = self.transition_probabilities[current_value]
        
        # 找出概率最高的值
        best_prediction = max(probabilities.items(), key=lambda x: x[1])
        predicted_value = best_prediction[0]
        predicted_probability = best_prediction[1]
        
        return predicted_value, predicted_probability, probabilities
    
    def get_top_predictions(self, current_value, top_n=5):
        """
        获取前N个最可能的预测值
        
        Args:
            current_value (int): 当前值
            top_n (int): 返回前N个预测
            
        Returns:
            list: [(预测值, 概率), ...]
        """
        if current_value not in self.transition_probabilities:
            global_freq = Counter(self.time_ordered_data)
            total = len(self.time_ordered_data)
            probabilities = {state: count/total for state, count in global_freq.items()}
        else:
            probabilities = self.transition_probabilities[current_value]
        
        # 按概率排序
        sorted_predictions = sorted(probabilities.items(), key=lambda x: x[1], reverse=True)
        return sorted_predictions[:top_n]
    
    def validate_model(self, test_ratio=0.2):
        """
        验证模型准确性
        
        Args:
            test_ratio (float): 测试集比例
            
        Returns:
            float: 准确率
        """
        print(f"\n=== 模型验证 (测试集比例: {test_ratio}) ===")
        
        # 分割数据
        split_point = int(len(self.time_ordered_data) * (1 - test_ratio))
        train_data = self.time_ordered_data[:split_point]
        test_data = self.time_ordered_data[split_point:]
        
        # 使用训练数据重新构建模型
        original_data = self.time_ordered_data
        self.time_ordered_data = train_data
        self.build_transition_matrix()
        
        # 在测试集上验证
        correct_predictions = 0
        total_predictions = 0
        
        for i in range(len(test_data) - 1):
            current_value = test_data[i]
            actual_next = test_data[i + 1]
            
            predicted_value, _, _ = self.predict_next_value(current_value)
            
            if predicted_value == actual_next:
                correct_predictions += 1
            total_predictions += 1
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        
        print(f"测试样本数: {total_predictions}")
        print(f"正确预测数: {correct_predictions}")
        print(f"准确率: {accuracy:.3f} ({accuracy*100:.1f}%)")
        
        # 恢复原始数据
        self.time_ordered_data = original_data
        self.build_transition_matrix()
        
        return accuracy
    
    def generate_analysis_report(self):
        """生成分析报告"""
        report = {
            "分析时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "数据统计": {
                "总记录数": len(self.time_ordered_data),
                "数据范围": f"{min(self.states)}-{max(self.states)}",
                "最新值": self.raw_data[0]
            },
            "频率分析": {},
            "转移概率矩阵": {},
            "模型验证": {}
        }
        
        # 频率分析
        freq_counter = Counter(self.time_ordered_data)
        total_count = len(self.time_ordered_data)
        for value in sorted(self.states):
            count = freq_counter[value]
            frequency = count / total_count
            report["频率分析"][str(value)] = {
                "出现次数": count,
                "频率": round(frequency, 4),
                "百分比": f"{frequency*100:.2f}%"
            }
        
        # 转移概率矩阵
        for current_state in sorted(self.states):
            if current_state in self.transition_probabilities:
                report["转移概率矩阵"][str(current_state)] = {}
                for next_state in sorted(self.states):
                    prob = self.transition_probabilities[current_state].get(next_state, 0)
                    if prob > 0:
                        report["转移概率矩阵"][str(current_state)][str(next_state)] = round(prob, 4)
        
        return report
    
    def run_analysis(self):
        """运行完整分析"""
        print("开始马尔科夫链分析...")
        print("=" * 50)
        
        # 加载数据
        self.load_data()
        
        # 构建转移矩阵
        self.build_transition_matrix()
        
        # 分析转移模式
        self.analyze_transition_patterns()
        
        # 模型验证
        accuracy = self.validate_model()
        
        # 基于最新数据进行预测
        current_value = self.raw_data[0]  # 最新的值（原文件第一行）
        predicted_value, predicted_prob, all_probs = self.predict_next_value(current_value)
        
        print(f"\n=== 预测结果 ===")
        print(f"当前值（最新一期）: {current_value}")
        print(f"预测下一期最可能的值: {predicted_value}")
        print(f"预测概率: {predicted_prob:.3f} ({predicted_prob*100:.1f}%)")
        
        # 显示前5个最可能的预测
        print(f"\n前5个最可能的预测:")
        top_predictions = self.get_top_predictions(current_value, 5)
        for i, (value, prob) in enumerate(top_predictions, 1):
            print(f"{i}. 数字 {value}: {prob:.3f} ({prob*100:.1f}%)")
        
        # 生成报告
        report = self.generate_analysis_report()
        report["模型验证"]["准确率"] = round(accuracy, 4)
        report["预测结果"] = {
            "当前值": current_value,
            "预测值": predicted_value,
            "预测概率": round(predicted_prob, 4),
            "前5预测": [(value, round(prob, 4)) for value, prob in top_predictions]
        }
        
        # 保存报告
        report_file = "analysisball/ssq/markov_analysis_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n分析报告已保存到: {report_file}")
        
        return predicted_value, predicted_prob, report


def main():
    """主函数"""
    # 创建分析器
    analyzer = MarkovBlueBallAnalyzer("analysisball/ssq/blueball.txt")
    
    # 运行分析
    predicted_value, predicted_prob, report = analyzer.run_analysis()
    
    print("\n" + "=" * 50)
    print("马尔科夫链分析完成！")
    print(f"最终预测: 下一期蓝球最可能是 {predicted_value} (概率: {predicted_prob*100:.1f}%)")
    print("=" * 50)
    
    return predicted_value


if __name__ == "__main__":
    main()