#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D彩票机器学习预测模块
功能：
1. 特征工程 - 构建预测特征
2. 朴素贝叶斯分类器 - 基于历史频率的概率预测
3. 决策树模型 - 基于规则的预测
4. 神经网络模型 - 深度学习预测（简化版）
5. 集成学习 - 多模型融合预测
"""

import json
import os
from collections import defaultdict, Counter
from typing import Dict, List, Tuple
import random
import math
import pandas as pd
 
class SimpleMachineLearning3D:
    """简化版3D彩票机器学习预测器"""
 
    def __init__(self, data_file: str = "analysisball/3d/3d.xlsx"):
        """
        初始化机器学习预测器
 
        Args:
            data_file: 数据文件路径
        """
        self.data_file = data_file
        self.data_df = None
        self.data = []
        self.loaded = False
        self.models = {}
 
    def load_data_from_excel(self):
        """从Excel文件加载数据"""
        try:
            if not os.path.exists(self.data_file):
                print(f"错误: 数据文件不存在于 {self.data_file}")
                return

            self.data_df = pd.read_excel(self.data_file)
            
            # 数据清洗和转换
            self.data_df = self.data_df.sort_values(by="期号", ascending=True).reset_index(drop=True)
            column_mapping = {
                '百位': 'hundreds', '十位': 'tens', '个位': 'units',
                '试机号百位': 'trial_hundreds', '试机号十位': 'trial_tens', '试机号个位': 'trial_units'
            }
            # 只重命名存在的列
            self.data_df.rename(columns={k: v for k, v in column_mapping.items() if k in self.data_df.columns}, inplace=True)
            
            # 将DataFrame转换为字典列表
            self.data = self.data_df.to_dict('records')
            
            self.loaded = True
            print(f"成功从 {self.data_file} 加载 {len(self.data)} 条真实数据用于机器学习")
 
        except Exception as e:
            print(f"从Excel加载数据失败: {e}")
            raise

    def extract_features(self, window_size: int = 10, target_issue: int = None) -> List[Dict]:
        """
        特征工程 - 提取预测特征

        Args:
            window_size: 历史窗口大小
            target_issue: 目标期号，用于筛选训练数据

        Returns:
            特征列表
        """
        if not self.loaded:
            self.load_data_from_excel()

        data_source = self.data
        if target_issue is not None:
            # 使用期号小于 target_issue 的数据进行特征提取
            filtered_df = self.data_df[self.data_df['期号'] < target_issue]
            data_source = filtered_df.to_dict('records')
            if not data_source:
                print(f"警告: 没有找到小于期号 {target_issue} 的数据，无法提取特征。")
                return []
            print(f"为预测期号 {target_issue}，使用 {len(data_source)} 条历史数据进行特征提取。")

        features = []

        for i in range(window_size, len(data_source)):
            # 确保数据有效
            if pd.isna(data_source[i]['hundreds']) or pd.isna(data_source[i]['tens']) or pd.isna(data_source[i]['units']):
                continue

            # 当前期的目标值
            target = {
                'hundreds': int(data_source[i]['hundreds']),
                'tens': int(data_source[i]['tens']),
                'units': int(data_source[i]['units'])
            }
 
            # 历史窗口特征
            window_data = data_source[i-window_size:i]

            feature_dict = {
                'target': target,
                'features': {}
            }

            # 添加试机号作为特征
            current_item = data_source[i]
            if 'trial_hundreds' in current_item and pd.notna(current_item.get('trial_hundreds')):
                feature_dict['features']['trial_hundreds'] = int(current_item['trial_hundreds'])
                feature_dict['features']['trial_tens'] = int(current_item['trial_tens'])
                feature_dict['features']['trial_units'] = int(current_item['trial_units'])
            else:
                # 如果没有试机号，使用-1作为占位符
                feature_dict['features']['trial_hundreds'] = -1
                feature_dict['features']['trial_tens'] = -1
                feature_dict['features']['trial_units'] = -1
 
            # 特征1：历史频率特征
            for pos in ['hundreds', 'tens', 'units']:
                pos_values = [item[pos] for item in window_data]
                feature_dict['features'][f'{pos}_freq'] = dict(Counter(pos_values))
                feature_dict['features'][f'{pos}_last'] = pos_values[-1]
                feature_dict['features'][f'{pos}_last_2'] = pos_values[-2] if len(pos_values) >= 2 else 0
                feature_dict['features'][f'{pos}_last_3'] = pos_values[-3] if len(pos_values) >= 3 else 0
 
            # 特征2：和值特征
            sums = [item['hundreds'] + item['tens'] + item['units'] for item in window_data]
            feature_dict['features']['sum_avg'] = sum(sums) / len(sums)
            feature_dict['features']['sum_last'] = sums[-1]
 
            # 特征3：跨度特征
            spans = [max(item['hundreds'], item['tens'], item['units']) -
                    min(item['hundreds'], item['tens'], item['units']) for item in window_data]
            feature_dict['features']['span_avg'] = sum(spans) / len(spans)
            feature_dict['features']['span_last'] = spans[-1]
 
            # 特征4：奇偶特征
            odd_counts = {'hundreds': 0, 'tens': 0, 'units': 0}
            for item in window_data:
                for pos in ['hundreds', 'tens', 'units']:
                    if item[pos] % 2 == 1:
                        odd_counts[pos] += 1
 
            for pos in ['hundreds', 'tens', 'units']:
                feature_dict['features'][f'{pos}_odd_ratio'] = odd_counts[pos] / len(window_data)

            features.append(feature_dict)

        return features

    def naive_bayes_classifier(self, position: str, features: List[Dict], target_issue: int = None) -> Dict[int, float]:
        """
        朴素贝叶斯分类器

        Args:
            position: 位置 ('hundreds', 'tens', 'units')
            features: 特征数据

        Returns:
            各数字的预测概率
        """
        # 统计先验概率
        target_counts = Counter([f['target'][position] for f in features])
        total_samples = len(features)
        prior_probs = {digit: count / total_samples for digit, count in target_counts.items()}

        # 为缺失的数字添加平滑
        for digit in range(10):
            if digit not in prior_probs:
                prior_probs[digit] = 1 / (total_samples + 10)  # 拉普拉斯平滑

        # 计算条件概率（简化版本，只考虑最近一期的数字）
        conditional_probs = defaultdict(lambda: defaultdict(float))

        for digit in range(10):
            digit_features = [f for f in features if f['target'][position] == digit]

            if digit_features:
                last_values = [f['features'][f'{position}_last'] for f in digit_features]
                last_counter = Counter(last_values)

                for last_val, count in last_counter.items():
                    conditional_probs[digit][last_val] = count / len(digit_features)

        # 预测（基于最后一期的数字）
        if features:
            last_feature = features[-1]['features'][f'{position}_last']

            predictions = {}
            for digit in range(10):
                likelihood = conditional_probs[digit].get(last_feature, 0.1)  # 平滑
                predictions[digit] = prior_probs[digit] * likelihood

            # 归一化
            total_prob = sum(predictions.values())
            if total_prob > 0:
                predictions = {k: v / total_prob for k, v in predictions.items()}

            return predictions

        return prior_probs

    def decision_tree_classifier(self, position: str, features: List[Dict], target_issue: int = None) -> Dict[int, float]:
        """
        简化版决策树分类器

        Args:
            position: 位置
            features: 特征数据

        Returns:
            各数字的预测概率
        """
        if not features:
            return {i: 0.1 for i in range(10)}

        # 简单的决策规则
        last_feature = features[-1]['features']
        last_digit = last_feature[f'{position}_last']
        last_2_digit = last_feature[f'{position}_last_2']

        predictions = {i: 0.05 for i in range(10)}  # 基础概率

        # 规则1：如果最近两期相同，下期不太可能再相同
        if last_digit == last_2_digit:
            predictions[last_digit] *= 0.5
            for i in range(10):
                if i != last_digit:
                    predictions[i] *= 1.1

        # 规则2：相邻数字有更高概率
        adjacent_digits = [(last_digit - 1) % 10, (last_digit + 1) % 10]
        for adj in adjacent_digits:
            predictions[adj] *= 1.3

        # 规则3：基于奇偶性
        odd_ratio = last_feature[f'{position}_odd_ratio']
        if odd_ratio > 0.6:  # 最近奇数较多
            for i in range(0, 10, 2):  # 偶数
                predictions[i] *= 1.2
        elif odd_ratio < 0.4:  # 最近偶数较多
            for i in range(1, 10, 2):  # 奇数
                predictions[i] *= 1.2

        # 归一化
        total_prob = sum(predictions.values())
        if total_prob > 0:
            predictions = {k: v / total_prob for k, v in predictions.items()}

        return predictions

    def simple_neural_network(self, position: str, features: List[Dict], target_issue: int = None) -> Dict[int, float]:
        """
        简化版神经网络（实际上是加权平均）

        Args:
            position: 位置
            features: 特征数据

        Returns:
            各数字的预测概率
        """
        if not features:
            return {i: 0.1 for i in range(10)}

        # 权重（模拟神经网络的权重）
        weights = {
            'freq_weight': 0.4,
            'last_weight': 0.3,
            'pattern_weight': 0.2,
            'trend_weight': 0.1
        }

        predictions = {i: 0.0 for i in range(10)}

        # 基于频率的预测
        recent_features = features[-20:] if len(features) >= 20 else features
        freq_counter = Counter([f['target'][position] for f in recent_features])
        total_count = len(recent_features)

        for digit in range(10):
            freq_prob = freq_counter.get(digit, 0) / total_count
            predictions[digit] += weights['freq_weight'] * freq_prob

        # 基于最近数字的预测
        if features:
            last_digit = features[-1]['features'][f'{position}_last']
            for digit in range(10):
                if digit == last_digit:
                    predictions[digit] += weights['last_weight'] * 0.1  # 连续概率较低
                else:
                    predictions[digit] += weights['last_weight'] * 0.1

        # 基于模式的预测
        for digit in range(10):
            predictions[digit] += weights['pattern_weight'] * 0.1

        # 基于趋势的预测
        for digit in range(10):
            predictions[digit] += weights['trend_weight'] * 0.1

        # 归一化
        total_prob = sum(predictions.values())
        if total_prob > 0:
            predictions = {k: v / total_prob for k, v in predictions.items()}

        return predictions