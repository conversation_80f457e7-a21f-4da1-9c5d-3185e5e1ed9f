#!/usr/bin/env python3
"""
贪吃蛇游戏测试脚本
"""

import sys
import os

def test_imports():
    """测试所有必要的模块是否能正常导入"""
    print("测试模块导入...")
    
    try:
        import pygame
        print("[成功] pygame 导入成功")
    except ImportError as e:
        print(f"[失败] pygame 导入失败: {e}")
        return False

    try:
        import config
        print("[成功] config 模块导入成功")
    except ImportError as e:
        print(f"[失败] config 模块导入失败: {e}")
        return False

    try:
        from snake_game import SnakeGame
        print("[成功] SnakeGame 类导入成功")
    except ImportError as e:
        print(f"[失败] SnakeGame 类导入失败: {e}")
        return False
    
    return True

def test_game_initialization():
    """测试游戏初始化"""
    print("\n测试游戏初始化...")
    
    try:
        import pygame
        pygame.init()
        
        from snake_game import SnakeGame
        game = SnakeGame()
        print("[成功] 游戏对象创建成功")

        # 测试游戏状态
        import config
        if game.game_state == config.GAME_STATES['MENU']:
            print("[成功] 初始游戏状态正确")
        else:
            print("[失败] 初始游戏状态错误")
            return False

        # 测试蛇的初始状态
        if len(game.snake) == 1:
            print("[成功] 蛇的初始长度正确")
        else:
            print("[失败] 蛇的初始长度错误")
            return False
        
        pygame.quit()
        return True
        
    except Exception as e:
        print(f"[失败] 游戏初始化失败: {e}")
        return False

def test_config():
    """测试配置文件"""
    print("\n测试配置文件...")
    
    try:
        import config
        
        # 检查必要的配置项
        required_colors = ['background', 'snake_head', 'snake_body', 'food']
        for color in required_colors:
            if color not in config.COLORS:
                print(f"[失败] 缺少颜色配置: {color}")
                return False

        print("[成功] 颜色配置完整")

        # 检查窗口尺寸
        if config.WINDOW_WIDTH > 0 and config.WINDOW_HEIGHT > 0:
            print("[成功] 窗口尺寸配置正确")
        else:
            print("[失败] 窗口尺寸配置错误")
            return False

        return True

    except Exception as e:
        print(f"[失败] 配置文件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("贪吃蛇游戏测试")
    print("=" * 50)
    
    tests = [
        ("模块导入测试", test_imports),
        ("配置文件测试", test_config),
        ("游戏初始化测试", test_game_initialization),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
            print(f"[通过] {test_name} 通过")
        else:
            print(f"[失败] {test_name} 失败")

    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")

    if passed == total:
        print("所有测试通过！游戏可以正常运行。")
        print("\n启动游戏:")
        print("python snake_game.py")
    else:
        print("部分测试失败，请检查错误信息。")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
