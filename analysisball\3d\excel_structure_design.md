# 3D彩票Excel扩展结构设计

## 当前Excel结构
- 日期: 开奖日期
- 期号: 期号
- 百位: 百位数字
- 十位: 十位数字  
- 个位: 个位数字
- 奖号: 完整号码数组

## 新增预测结果列设计

### 简单分析预测列
- 简单_百位_预测: 基于转移矩阵的百位预测数字
- 简单_百位_概率: 预测概率
- 简单_十位_预测: 基于转移矩阵的十位预测数字
- 简单_十位_概率: 预测概率
- 简单_个位_预测: 基于转移矩阵的个位预测数字
- 简单_个位_概率: 预测概率

### 马尔科夫链预测列
- 马尔科夫_百位_预测: 马尔科夫链百位预测
- 马尔科夫_百位_概率: 预测概率
- 马尔科夫_十位_预测: 马尔科夫链十位预测
- 马尔科夫_十位_概率: 预测概率
- 马尔科夫_个位_预测: 马尔科夫链个位预测
- 马尔科夫_个位_概率: 预测概率

### 时间序列预测列
- 时序_百位_预测: 时间序列百位预测
- 时序_百位_概率: 预测概率
- 时序_十位_预测: 时间序列十位预测
- 时序_十位_概率: 预测概率
- 时序_个位_预测: 时间序列个位预测
- 时序_个位_概率: 预测概率

### 机器学习预测列
- ML_朴素贝叶斯_百位: 朴素贝叶斯百位预测
- ML_朴素贝叶斯_十位: 朴素贝叶斯十位预测
- ML_朴素贝叶斯_个位: 朴素贝叶斯个位预测
- ML_决策树_百位: 决策树百位预测
- ML_决策树_十位: 决策树十位预测
- ML_决策树_个位: 决策树个位预测
- ML_神经网络_百位: 神经网络百位预测
- ML_神经网络_十位: 神经网络十位预测
- ML_神经网络_个位: 神经网络个位预测

### 综合预测列
- 综合_百位_预测: 多算法综合百位预测
- 综合_十位_预测: 多算法综合十位预测
- 综合_个位_预测: 多算法综合个位预测
- 综合_完整号码: 综合预测的完整号码

### 准确率统计列
- 简单分析_准确率: 简单分析预测准确率
- 马尔科夫_准确率: 马尔科夫链预测准确率
- 时序_准确率: 时间序列预测准确率
- ML_准确率: 机器学习预测准确率
- 综合_准确率: 综合预测准确率

### 预测状态列
- 预测日期: 预测生成日期
- 预测状态: 是否已验证(待验证/已验证/准确/不准确)
- 备注: 其他备注信息

## 数据存储格式

### 预测数字格式
- 存储最高概率的预测数字(0-9)
- 如果有多个相同概率，存储最小的数字

### 概率格式
- 保留4位小数，如0.1234
- 范围0-1之间

### 准确率格式
- 百分比格式，如85.67%
- 基于最近100期的预测准确率

## 实现策略

1. **增量更新**: 只为新期号添加预测结果
2. **历史回填**: 可选择性地为历史数据生成预测结果
3. **性能优化**: 批量处理多期预测
4. **数据验证**: 确保预测结果的有效性

## 横向对比功能

1. **算法对比**: 不同算法在同一期的预测结果对比
2. **趋势分析**: 各算法准确率的时间趋势
3. **最佳算法**: 自动识别当前表现最好的算法
4. **组合策略**: 基于历史表现的算法权重组合
