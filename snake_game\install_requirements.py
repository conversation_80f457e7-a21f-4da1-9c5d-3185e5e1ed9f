#!/usr/bin/env python3
"""
安装贪吃蛇游戏所需的依赖包
"""

import subprocess
import sys

def install_package(package):
    """安装指定的包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("贪吃蛇游戏 - 依赖安装器")
    print("=" * 50)
    
    packages = ["pygame"]
    
    for package in packages:
        print(f"正在安装 {package}...")
        if install_package(package):
            print(f"[成功] {package} 安装成功!")
        else:
            print(f"[失败] {package} 安装失败!")
            print("请手动运行: pip install pygame")
            return

    print("\n所有依赖安装完成!")
    print("现在可以运行游戏了:")
    print("python snake_game.py")
    print("或者:")
    print("python run_game.py")

if __name__ == "__main__":
    main()
