#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D彩票分析系统简化版主程序
功能：
1. 运行基础分析
2. 生成分析报告
3. 展示分析结果
"""

import json
import os
from datetime import datetime

# 导入简单分析模块
try:
    from simple_analysis import Simple3DAnalyzer
except ImportError:
    print("无法导入simple_analysis模块，请检查文件是否存在")
    exit(1)

def main():
    """主函数"""
    print("3D彩票分析系统 - 简化版")
    print("=" * 50)

    try:
        # 创建分析器
        analyzer = Simple3DAnalyzer()

        print("正在运行3D彩票数据分析...")

        # 生成分析报告
        report = analyzer.generate_report()

        if report:
            print("\n分析完成！")
            print("=" * 50)

            # 显示关键结果
            print_key_results(report)

            # 生成预测建议
            print_predictions(report)

        else:
            print("分析失败，请检查数据源")

    except Exception as e:
        print(f"程序运行出错: {e}")

def print_key_results(report):
    """打印关键分析结果"""
    print("\n关键分析结果:")
    print("-" * 30)

    # 数据概览
    if 'summary' in report:
        summary = report['summary']
        print(f"数据总量: {summary['total_records']} 期")
        print(f"分析日期: {summary['analysis_date']}")

    # 频率分析结果
    if 'frequency_analysis' in report:
        freq_analysis = report['frequency_analysis']
        print("\n各位置热冷号码:")

        for position, data in freq_analysis.items():
            pos_name = data['position_name']
            most_freq = data['most_frequent']
            least_freq = data['least_frequent']
            max_count = data['max_frequency']
            min_count = data['min_frequency']

            print(f"  {pos_name}:")
            print(f"    最热号码: {most_freq} (出现{max_count}次)")
            print(f"    最冷号码: {least_freq} (出现{min_count}次)")

    # 模式分析结果
    if 'pattern_analysis' in report:
        patterns = report['pattern_analysis']
        print("\n号码模式分析:")

        consecutive = patterns['consecutive_patterns']
        print(f"  全相同号码: {consecutive['same_all']} 期")
        print(f"  两个相同号码: {consecutive['same_two']} 期")
        print(f"  其他模式: {consecutive['other']} 期")

        # 奇偶模式
        odd_even = patterns['odd_even_patterns']
        if odd_even:
            most_common_oe = max(odd_even.items(), key=lambda x: x[1])
            print(f"  最常见奇偶模式: {most_common_oe[0]} (出现{most_common_oe[1]}次)")

def print_predictions(report):
    """打印预测建议"""
    print("\n预测建议:")
    print("-" * 30)

    if 'predictions' in report:
        predictions = report['predictions']

        for position, pred_data in predictions.items():
            pos_name = {'hundreds': '百位', 'tens': '十位', 'units': '个位'}[position]
            current = pred_data['current_number']
            top_predictions = pred_data['predictions']

            print(f"\n{pos_name} (当前: {current}):")
            print("  推荐号码:")

            for i, (number, probability) in enumerate(top_predictions[:3], 1):
                print(f"    {i}. 数字 {number} (概率: {probability:.3f})")

    print("\n注意事项:")
    print("- 以上预测仅基于历史数据的统计分析")
    print("- 彩票具有随机性，预测仅供参考")
    print("- 请理性购彩，量力而行")

if __name__ == "__main__":
    main()