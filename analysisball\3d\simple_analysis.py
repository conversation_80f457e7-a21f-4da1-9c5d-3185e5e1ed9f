#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D彩票简化分析模块
功能：
1. 基础频率统计
2. 简单马尔科夫链分析
3. 数字转移概率计算
4. 基础预测功能
"""

import json
import os
from collections import defaultdict, Counter
from typing import Dict, List, Tuple
import pandas as pd
 
class Simple3DAnalyzer:
    """简化版3D彩票分析器"""
 
    def __init__(self, data_file: str = "analysisball/3d/3d.xlsx"):
        """
        初始化分析器
 
        Args:
            data_file: 数据文件路径
        """
        self.data_file = data_file
        self.data_df = None
        self.data = []
        self.loaded = False
 
    def load_data_from_excel(self):
        """从Excel文件加载数据"""
        try:
            if not os.path.exists(self.data_file):
                print(f"错误: 数据文件不存在于 {self.data_file}")
                return
            
            self.data_df = pd.read_excel(self.data_file)
            
            # 数据清洗和转换
            self.data_df = self.data_df.sort_values(by="期号", ascending=True).reset_index(drop=True)
            column_mapping = {
                '百位': 'hundreds', '十位': 'tens', '个位': 'units',
                '试机号百位': 'trial_hundreds', '试机号十位': 'trial_tens', '试机号个位': 'trial_units'
            }
            # 只重命名存在的列
            self.data_df.rename(columns={k: v for k, v in column_mapping.items() if k in self.data_df.columns}, inplace=True)
            
            # 将DataFrame转换为字典列表以兼容旧代码
            self.data = self.data_df.to_dict('records')
            
            self.loaded = True
            print(f"成功从 {self.data_file} 加载 {len(self.data)} 条真实数据")
 
        except Exception as e:
            print(f"从Excel加载数据失败: {e}")
            raise

    def frequency_analysis(self) -> Dict[str, Dict]:
        """频率分析"""
        if not self.loaded:
            self.load_data_from_excel()

        positions = ['hundreds', 'tens', 'units']
        position_names = ['百位', '十位', '个位']

        results = {}

        for pos, pos_name in zip(positions, position_names):
            # 统计频率
            frequency = Counter([item[pos] for item in self.data])
            total_count = len(self.data)

            # 计算概率
            probability = {k: v / total_count for k, v in frequency.items()}

            # 找出最热和最冷的数字
            most_frequent = max(frequency, key=frequency.get)
            least_frequent = min(frequency, key=frequency.get)

            results[pos] = {
                'position_name': pos_name,
                'frequency': dict(frequency),
                'probability': probability,
                'most_frequent': most_frequent,
                'least_frequent': least_frequent,
                'max_frequency': frequency[most_frequent],
                'min_frequency': frequency[least_frequent]
            }

        return results

    def build_transition_matrix(self, position: str) -> Dict[int, Dict[int, float]]:
        """
        构建一阶马尔科夫链转移矩阵
 
        Args:
            position: 位置 ('hundreds', 'tens', 'units')
 
        Returns:
            转移矩阵字典
        """
        if not self.loaded:
            self.load_data_from_excel()
 
        # 获取数字序列
        sequence = [item[position] for item in self.data if position in item and pd.notna(item[position])]

        # 统计转移次数
        transition_counts = defaultdict(lambda: defaultdict(int))

        for i in range(len(sequence) - 1):
            current_state = sequence[i]
            next_state = sequence[i + 1]
            transition_counts[current_state][next_state] += 1

        # 转换为概率矩阵
        transition_matrix = {}

        for current_state in range(10):
            transition_matrix[current_state] = {}
            total_transitions = sum(transition_counts[current_state].values())

            if total_transitions > 0:
                for next_state in range(10):
                    count = transition_counts[current_state][next_state]
                    transition_matrix[current_state][next_state] = count / total_transitions
            else:
                # 如果没有转移数据，使用均匀分布
                for next_state in range(10):
                    transition_matrix[current_state][next_state] = 0.1

        return transition_matrix

    def predict_next_numbers(self, position: str, current_number: int, top_k: int = 3) -> List[Tuple[int, float]]:
        """
        预测下期可能出现的数字

        Args:
            position: 位置
            current_number: 当前数字
            top_k: 返回概率最高的前k个数字

        Returns:
            [(数字, 概率), ...] 按概率降序排列
        """
        transition_matrix = self.build_transition_matrix(position)

        # 获取当前数字的转移概率
        probabilities = transition_matrix[current_number]

        # 按概率排序
        sorted_probs = sorted(probabilities.items(), key=lambda x: x[1], reverse=True)

        return sorted_probs[:top_k]

    def analyze_patterns(self) -> Dict[str, any]:
        """分析数字模式"""
        if not self.loaded:
            self.load_data_from_excel()

        # 分析连号情况
        consecutive_patterns = {
            'ascending': 0,    # 递增 (如: 123)
            'descending': 0,   # 递减 (如: 321)
            'same_all': 0,     # 全相同 (如: 111)
            'same_two': 0,     # 两个相同 (如: 112, 121, 211)
            'other': 0         # 其他
        }

        # 分析奇偶模式
        odd_even_patterns = Counter()

        # 分析大小模式（5以上为大，4以下为小）
        size_patterns = Counter()

        for item in self.data:
            h, t, u = item['hundreds'], item['tens'], item['units']

            # 连号分析
            if h == t == u:
                consecutive_patterns['same_all'] += 1
            elif h == t or t == u or h == u:
                consecutive_patterns['same_two'] += 1
            elif (h + 1 == t and t + 1 == u) or (h == t + 1 and t == u + 1):
                if h < t < u:
                    consecutive_patterns['ascending'] += 1
                else:
                    consecutive_patterns['descending'] += 1
            else:
                consecutive_patterns['other'] += 1

            # 奇偶分析
            odd_even = ''.join(['奇' if x % 2 == 1 else '偶' for x in [h, t, u]])
            odd_even_patterns[odd_even] += 1

            # 大小分析
            size = ''.join(['大' if x >= 5 else '小' for x in [h, t, u]])
            size_patterns[size] += 1

        return {
            'consecutive_patterns': consecutive_patterns,
            'odd_even_patterns': dict(odd_even_patterns),
            'size_patterns': dict(size_patterns)
        }

    def analyze_trial_vs_winning(self) -> Dict:
        """分析试机号与中奖号码的关系"""
        if not self.loaded:
            self.load_data_from_excel()

        # 确保DataFrame已加载
        if self.data_df is None:
            return {}

        # 检查必要的列是否存在
        required_cols = ['trial_hundreds', 'trial_tens', 'trial_units', 'hundreds', 'tens', 'units']
        if not all(col in self.data_df.columns for col in required_cols):
            return {'message': '缺少必要的试机号或中奖号码列，跳过分析'}

        # 筛选出同时有试机号和中奖号的数据
        valid_df = self.data_df.dropna(subset=required_cols).copy()
        
        # 将列转换为数字类型
        for col in required_cols:
            valid_df.loc[:, col] = pd.to_numeric(valid_df[col])

        if valid_df.empty:
            return {'message': '没有足够的有效数据进行试机号分析'}

        results = {
            'total_valid_samples': len(valid_df),
            'full_match_count': 0,
            'positional_matches': {'hundreds': 0, 'tens': 0, 'units': 0},
            'difference_distribution': {'hundreds': [], 'tens': [], 'units': []}
        }

        # 计算完全匹配
        full_match = (valid_df['trial_hundreds'] == valid_df['hundreds']) & \
                     (valid_df['trial_tens'] == valid_df['tens']) & \
                     (valid_df['trial_units'] == valid_df['units'])
        results['full_match_count'] = int(full_match.sum())

        # 计算按位匹配
        results['positional_matches']['hundreds'] = int((valid_df['trial_hundreds'] == valid_df['hundreds']).sum())
        results['positional_matches']['tens'] = int((valid_df['trial_tens'] == valid_df['tens']).sum())
        results['positional_matches']['units'] = int((valid_df['trial_units'] == valid_df['units']).sum())

        # 计算差值分布
        results['difference_distribution']['hundreds'] = (valid_df['hundreds'] - valid_df['trial_hundreds']).tolist()
        results['difference_distribution']['tens'] = (valid_df['tens'] - valid_df['trial_tens']).tolist()
        results['difference_distribution']['units'] = (valid_df['units'] - valid_df['trial_units']).tolist()

        return results

    def generate_report(self, output_file: str = "analysisball/3d/simple_analysis_report.json", target_issue: int = None):
        """
        生成分析报告
        Args:
            output_file (str): 报告输出路径
            target_issue (int, optional): 目标期号. 如果提供，则只分析此期号（含）之前的数据.
        """
        print("开始生成3D彩票分析报告...")

        if not self.loaded:
            self.load_data_from_excel()

        # 保存原始数据
        original_data = self.data
        original_data_df = self.data_df

        try:
            # 如果提供了 target_issue，则筛选数据
            if target_issue:
                print(f"检测到目标期号: {target_issue}, 将筛选数据进行分析...")
                if '期号' not in self.data_df.columns:
                    print("错误: DataFrame中缺少'期号'列，无法按期号筛选。")
                    return None
                
                # 筛选小于等于 target_issue 的数据
                filtered_df = self.data_df[self.data_df['期号'] <= target_issue].copy()
                
                if filtered_df.empty:
                    print(f"警告: 没有找到小于或等于期号 {target_issue} 的数据。")
                    return None
                
                self.data_df = filtered_df
                self.data = filtered_df.to_dict('records')
                print(f"数据筛选后，剩余 {len(self.data)} 条记录。")

            # 频率分析
            print("1. 进行频率分析...")
            frequency_results = self.frequency_analysis()
    
            # 马尔科夫链分析
            print("2. 构建马尔科夫链转移矩阵...")
            transition_matrices = {}
            for position in ['hundreds', 'tens', 'units']:
                transition_matrices[position] = self.build_transition_matrix(position)
    
            # 模式分析
            print("3. 分析数字模式...")
            pattern_results = self.analyze_patterns()
            
            # 试机号 vs 中奖号分析
            print("4. 分析试机号与中奖号的关系...")
            trial_vs_winning_results = self.analyze_trial_vs_winning()
    
            # 预测示例 (基于筛选后数据的最后一期)
            print("5. 生成预测示例...")
            predictions = {}
            if self.data:
                last_issue_data = self.data[-1]
                last_numbers = {
                    'hundreds': last_issue_data['hundreds'],
                    'tens': last_issue_data['tens'],
                    'units': last_issue_data['units']
                }
    
                for position, number in last_numbers.items():
                    # 确保 current_number 是整数
                    if pd.notna(number):
                        predictions[position] = {
                            'current_number': int(number),
                            'predictions': self.predict_next_numbers(position, int(number), top_k=5)
                        }
                    else:
                        predictions[position] = {
                             'current_number': None,
                             'predictions': []
                        }
    
            # 汇总报告
            report = {
                'summary': {
                    'total_records': len(self.data),
                    'analysis_date': pd.Timestamp.now().strftime('%Y-%m-%d'),
                    'data_type': 'real',
                    'target_issue': target_issue if target_issue else 'latest'
                },
                'frequency_analysis': frequency_results,
                'transition_matrices': transition_matrices,
                'pattern_analysis': pattern_results,
                'trial_vs_winning_analysis': trial_vs_winning_results,
                'predictions': predictions
            }
    
            # 保存报告
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=int)
    
            print(f"分析报告已保存到: {output_file}")
    
            # 打印关键结果
            self.print_summary(report)
    
            return report

        finally:
            # 恢复原始数据
            self.data = original_data
            self.data_df = original_data_df
            print("分析完成，已恢复原始数据。")

    def print_summary(self, report: Dict):
        """打印分析摘要"""
        print("\n" + "="*50)
        print("3D彩票分析摘要")
        print("="*50)

        print(f"数据总量: {report['summary']['total_records']} 期")

        print("\n频率分析结果:")
        for position, data in report['frequency_analysis'].items():
            pos_name = data['position_name']
            most_freq = data['most_frequent']
            least_freq = data['least_frequent']
            print(f"  {pos_name}: 最热号码 {most_freq} ({data['max_frequency']}次), "
                  f"最冷号码 {least_freq} ({data['min_frequency']}次)")

        print("\n模式分析结果:")
        patterns = report['pattern_analysis']
        print(f"  全相同号码: {patterns['consecutive_patterns']['same_all']} 期")
        print(f"  两个相同号码: {patterns['consecutive_patterns']['same_two']} 期")

        print("\n基于马尔科夫链的预测:")
        for position, pred_data in report['predictions'].items():
            pos_name = {'hundreds': '百位', 'tens': '十位', 'units': '个位'}[position]
            current = pred_data['current_number']
            top_pred = pred_data['predictions'][0]
            print(f"  {pos_name}: 当前数字 {current} -> 预测数字 {top_pred[0]} (概率: {top_pred[1]:.3f})")

def main():
    """主函数"""
    analyzer = Simple3DAnalyzer()

    try:
        # 生成分析报告
        report = analyzer.generate_report()

        print("\n分析完成！")
        print("注意：由于环境限制，本次使用的是模拟数据。")
        print("在实际使用中，请确保安装pandas等依赖包，并使用真实的3D彩票数据。")

    except Exception as e:
        print(f"分析过程中出错: {e}")

if __name__ == "__main__":
    main()