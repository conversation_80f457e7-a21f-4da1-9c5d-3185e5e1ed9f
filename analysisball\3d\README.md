# 3D彩票数据收集与分析系统

## 项目概述

这是一个专门用于3D彩票历史开奖数据收集、存储和分析的综合系统。系统应用了多种数学模型和统计方法，包括马尔科夫链、时间序列分析、机器学习等，旨在发现3D彩票号码的潜在规律和模式。

## 系统架构

### 核心模块

1. **数据收集模块** (`spyder.py`, `enhanced_spider.py`)
   - 从中国福利彩票官网自动获取3D彩票历史开奖数据
   - 支持增量更新和数据验证
   - 具备错误处理和重试机制

2. **简单分析模块** (`simple_analysis.py`)
   - 基础频率统计分析
   - 简化版马尔科夫链分析
   - 数字转移概率计算
   - 基础预测功能

3. **马尔科夫链分析模块** (`markov_analysis.py`)
   - 构建一阶和二阶马尔科夫链转移矩阵
   - 计算稳态分布
   - 分析百位、十位、个位数字的状态转移规律
   - 基于马尔科夫链的预测

4. **统计学分析模块** (`statistical_analysis.py`)
   - 频率分析和分布分析
   - 相关性分析
   - 冷热号码分析
   - 间隔分析和组合分析

5. **时间序列分析模块** (`time_series_analysis.py`)
   - 周期性分析
   - 季节性分析
   - 趋势分析
   - 自相关分析

6. **机器学习预测模块** (`ml_prediction.py`)
   - 特征工程
   - 朴素贝叶斯分类器
   - 决策树模型
   - 简化版神经网络

7. **主控制器** (`main_controller.py`, `simple_main.py`)
   - 整合所有分析模块
   - 生成综合分析报告
   - 提供统一的分析接口

## 主要功能

### 数据分析功能

- **频率分析**: 统计各位置数字的出现频率，识别热号和冷号
- **模式分析**: 分析数字组合模式，如全相同、两个相同等
- **马尔科夫链分析**: 研究数字序列的状态转移概率
- **时间序列分析**: 检测周期性和季节性规律
- **机器学习预测**: 使用多种算法进行号码预测

### 预测功能

- **基于频率的预测**: 根据历史频率推荐号码
- **基于马尔科夫链的预测**: 根据状态转移概率预测下期号码
- **基于机器学习的预测**: 使用多种模型进行集成预测

## 文件结构

```
analysisball/3d/
├── README.md                    # 项目说明文档
├── spyder.py                    # 原始数据爬虫
├── enhanced_spider.py           # 增强版数据爬虫
├── simple_analysis.py           # 简单分析模块
├── markov_analysis.py           # 马尔科夫链分析模块
├── statistical_analysis.py     # 统计学分析模块
├── time_series_analysis.py     # 时间序列分析模块
├── ml_prediction.py             # 机器学习预测模块
├── main_controller.py           # 主控制器
├── simple_main.py               # 简化版主程序
├── 3d.xlsx                      # 历史数据文件
├── 3d_data.db                   # SQLite数据库文件
└── reports/                     # 分析报告目录
    ├── simple_analysis_report.json
    └── comprehensive_analysis_report.json
```

## 快速开始

### 环境要求

- Python 3.7+
- 基础库：json, os, collections, datetime, random, math

### 可选依赖（用于完整功能）

```bash
pip install pandas numpy matplotlib seaborn scipy requests
```

### 运行分析

#### 方法1：运行简化版（推荐）

```bash
cd analysisball/3d
python simple_main.py
```

#### 方法2：运行完整版（需要安装依赖）

```bash
cd analysisball/3d
python main_controller.py
```

#### 方法3：单独运行各模块

```bash
# 运行简单分析
python simple_analysis.py

# 运行马尔科夫链分析（需要依赖）
python markov_analysis.py

# 更新数据
python spyder.py
```

## 分析结果示例

### 频率分析结果

```
各位置热冷号码:
  百位:
    最热号码: 3 (出现112次)
    最冷号码: 8 (出现87次)
  十位:
    最热号码: 8 (出现117次)
    最冷号码: 0 (出现86次)
  个位:
    最热号码: 5 (出现112次)
    最冷号码: 0 (出现83次)
```

### 预测建议

```
百位 (当前: 9):
  推荐号码:
    1. 数字 2 (概率: 0.125)
    2. 数字 8 (概率: 0.125)
    3. 数字 0 (概率: 0.115)
```

## 数学模型说明

### 马尔科夫链模型

马尔科夫链用于分析数字序列的状态转移概率：

- **一阶马尔科夫链**: P(X_{n+1} = j | X_n = i)
- **二阶马尔科夫链**: P(X_{n+1} = k | X_n = j, X_{n-1} = i)
- **稳态分布**: 长期状态分布π，满足π = πP

### 统计分析方法

- **频率分析**: 计算各数字的出现频率和概率
- **卡方检验**: 检验数字分布的均匀性
- **相关性分析**: 分析不同位置数字之间的相关性
- **独立性检验**: 检验位置间的独立性

### 机器学习方法

- **朴素贝叶斯**: P(Y|X) = P(X|Y)P(Y)/P(X)
- **决策树**: 基于规则的分类预测
- **特征工程**: 提取历史频率、奇偶性、和值等特征

## 注意事项

1. **数据来源**: 数据来自中国福利彩票官网，请确保网络连接正常
2. **预测准确性**: 彩票具有随机性，所有预测仅供参考
3. **理性购彩**: 请理性对待彩票，量力而行
4. **环境兼容性**: 简化版可在基础Python环境运行，完整版需要额外依赖

## 开发计划

- [ ] 添加更多机器学习模型
- [ ] 实现Web界面
- [ ] 添加实时数据更新
- [ ] 优化预测算法
- [ ] 添加更多可视化功能

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 免责声明

本项目仅用于学习和研究目的。彩票投注有风险，请理性购彩。作者不对使用本系统进行彩票投注造成的任何损失承担责任。