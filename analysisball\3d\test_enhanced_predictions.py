#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版预测功能
直接使用现有数据测试预测结果的生成和Excel写入
"""

import pandas as pd
import os
from datetime import datetime

# 导入分析模块
try:
    from simple_analysis import Simple3DAnalyzer
    print("[成功] 导入simple_analysis模块")
except ImportError as e:
    print(f"[失败] 导入simple_analysis模块失败: {e}")
    Simple3DAnalyzer = None

try:
    from ml_prediction import SimpleMachineLearning3D
    print("[成功] 导入ml_prediction模块")
except ImportError as e:
    print(f"[失败] 导入ml_prediction模块失败: {e}")
    SimpleMachineLearning3D = None

class PredictionTester:
    """预测功能测试器"""
    
    def __init__(self, data_dir="analysisball/3d"):
        self.data_dir = data_dir
        self.excel_file = os.path.join(data_dir, "3d.xlsx")
        self.analyzers = {}
        self._init_analyzers()
    
    def _init_analyzers(self):
        """初始化分析器"""
        if os.path.exists(self.excel_file):
            if Simple3DAnalyzer:
                try:
                    self.analyzers['simple'] = Simple3DAnalyzer(self.excel_file)
                    print("[成功] 简单分析器初始化成功")
                except Exception as e:
                    print(f"[失败] 简单分析器初始化失败: {e}")
            
            if SimpleMachineLearning3D:
                try:
                    self.analyzers['ml'] = SimpleMachineLearning3D(self.excel_file)
                    print("[成功] 机器学习分析器初始化成功")
                except Exception as e:
                    print(f"[失败] 机器学习分析器初始化失败: {e}")
    
    def add_prediction_columns(self, df):
        """添加预测结果列"""
        prediction_columns = [
            # 简单分析预测列
            "简单_百位_预测", "简单_百位_概率",
            "简单_十位_预测", "简单_十位_概率", 
            "简单_个位_预测", "简单_个位_概率",
            
            # 机器学习预测列
            "ML_朴素贝叶斯_百位", "ML_朴素贝叶斯_十位", "ML_朴素贝叶斯_个位",
            
            # 综合预测列
            "综合_百位_预测", "综合_十位_预测", "综合_个位_预测",
            "综合_完整号码",
            
            # 准确率统计列
            "简单分析_准确率", "ML_准确率",
            
            # 预测状态列
            "预测日期", "预测状态", "备注"
        ]
        
        for col in prediction_columns:
            if col not in df.columns:
                df[col] = None
        
        return df
    
    def generate_predictions_for_recent_data(self, df, num_periods=10):
        """为最近的数据生成预测结果"""
        print(f"\n为最近{num_periods}期数据生成预测结果...")
        
        if not self.analyzers:
            print("没有可用的分析器")
            return df
        
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 获取最近的期数
        recent_data = df.head(num_periods).copy()
        
        for idx, row in recent_data.iterrows():
            issue = row['期号']
            
            try:
                # 找到前一期数据
                prev_issue = issue - 1
                prev_data = df[df['期号'] == prev_issue]
                
                if len(prev_data) > 0:
                    prev_row = prev_data.iloc[0]
                    prev_hundreds = int(prev_row['百位'])
                    prev_tens = int(prev_row['十位'])
                    prev_units = int(prev_row['个位'])
                    
                    # 生成简单分析预测
                    if 'simple' in self.analyzers:
                        simple_predictions = self._get_simple_predictions(
                            self.analyzers['simple'], prev_hundreds, prev_tens, prev_units
                        )
                        
                        df.loc[idx, '简单_百位_预测'] = simple_predictions['hundreds']['digit']
                        df.loc[idx, '简单_百位_概率'] = round(simple_predictions['hundreds']['prob'], 4)
                        df.loc[idx, '简单_十位_预测'] = simple_predictions['tens']['digit']
                        df.loc[idx, '简单_十位_概率'] = round(simple_predictions['tens']['prob'], 4)
                        df.loc[idx, '简单_个位_预测'] = simple_predictions['units']['digit']
                        df.loc[idx, '简单_个位_概率'] = round(simple_predictions['units']['prob'], 4)
                        
                        # 综合预测（目前只有简单分析）
                        df.loc[idx, '综合_百位_预测'] = simple_predictions['hundreds']['digit']
                        df.loc[idx, '综合_十位_预测'] = simple_predictions['tens']['digit']
                        df.loc[idx, '综合_个位_预测'] = simple_predictions['units']['digit']
                        df.loc[idx, '综合_完整号码'] = f"{simple_predictions['hundreds']['digit']}{simple_predictions['tens']['digit']}{simple_predictions['units']['digit']}"
                    
                    # 生成机器学习预测
                    if 'ml' in self.analyzers:
                        ml_predictions = self._get_ml_predictions(self.analyzers['ml'], issue)
                        if ml_predictions:
                            for position in ['hundreds', 'tens', 'units']:
                                pos_name = {'hundreds': '百位', 'tens': '十位', 'units': '个位'}[position]
                                if position in ml_predictions:
                                    pred = ml_predictions[position]
                                    if 'naive_bayes' in pred:
                                        nb_best = max(pred['naive_bayes'].items(), key=lambda x: x[1])
                                        df.loc[idx, f'ML_朴素贝叶斯_{pos_name}'] = nb_best[0]
                    
                    # 设置预测状态
                    df.loc[idx, '预测日期'] = current_time
                    df.loc[idx, '预测状态'] = "已生成"
                    df.loc[idx, '备注'] = "测试生成"
                    
                    print(f"期号 {issue}: 预测完成")
                
            except Exception as e:
                print(f"为期号 {issue} 生成预测时出错: {e}")
                df.loc[idx, '预测状态'] = "预测失败"
                df.loc[idx, '备注'] = f"错误: {str(e)}"
        
        return df
    
    def _get_simple_predictions(self, analyzer, hundreds, tens, units):
        """获取简单分析预测结果"""
        predictions = {}
        
        positions = {
            'hundreds': hundreds,
            'tens': tens, 
            'units': units
        }
        
        for position, current_number in positions.items():
            try:
                pred_results = analyzer.predict_next_numbers(position, current_number, top_k=1)
                if pred_results:
                    digit, prob = pred_results[0]
                    predictions[position] = {'digit': digit, 'prob': prob}
                else:
                    predictions[position] = {'digit': 0, 'prob': 0.1}
            except Exception as e:
                print(f"预测 {position} 位时出错: {e}")
                predictions[position] = {'digit': 0, 'prob': 0.1}
        
        return predictions
    
    def _get_ml_predictions(self, analyzer, issue):
        """获取机器学习预测结果"""
        try:
            features = analyzer.extract_features(window_size=10, target_issue=issue)
            
            if features:
                predictions = {}
                
                for position in ['hundreds', 'tens', 'units']:
                    nb_pred = analyzer.naive_bayes_classifier(position, features, target_issue=issue)
                    predictions[position] = {'naive_bayes': nb_pred}
                
                return predictions
            else:
                return None
                
        except Exception as e:
            print(f"机器学习预测时出错: {e}")
            return None
    
    def calculate_accuracy(self, df):
        """计算预测准确率"""
        print("\n计算预测准确率...")
        
        try:
            has_prediction = df['简单_百位_预测'].notna()
            prediction_rows = df[has_prediction].copy()
            
            if len(prediction_rows) == 0:
                print("没有找到预测数据")
                return df
            
            # 计算简单分析准确率
            correct_hundreds = (prediction_rows['简单_百位_预测'] == prediction_rows['百位']).sum()
            correct_tens = (prediction_rows['简单_十位_预测'] == prediction_rows['十位']).sum()
            correct_units = (prediction_rows['简单_个位_预测'] == prediction_rows['个位']).sum()
            correct_complete = (
                (prediction_rows['简单_百位_预测'] == prediction_rows['百位']) &
                (prediction_rows['简单_十位_预测'] == prediction_rows['十位']) &
                (prediction_rows['简单_个位_预测'] == prediction_rows['个位'])
            ).sum()
            
            total_predictions = len(prediction_rows)
            
            hundreds_accuracy = (correct_hundreds / total_predictions) * 100
            tens_accuracy = (correct_tens / total_predictions) * 100
            units_accuracy = (correct_units / total_predictions) * 100
            complete_accuracy = (correct_complete / total_predictions) * 100
            
            print(f"简单分析准确率统计 (基于{total_predictions}期预测):")
            print(f"  百位准确率: {hundreds_accuracy:.2f}% ({correct_hundreds}/{total_predictions})")
            print(f"  十位准确率: {tens_accuracy:.2f}% ({correct_tens}/{total_predictions})")
            print(f"  个位准确率: {units_accuracy:.2f}% ({correct_units}/{total_predictions})")
            print(f"  完整号码准确率: {complete_accuracy:.2f}% ({correct_complete}/{total_predictions})")
            
            # 更新准确率列
            accuracy_text = f"百位:{hundreds_accuracy:.1f}% 十位:{tens_accuracy:.1f}% 个位:{units_accuracy:.1f}% 完整:{complete_accuracy:.1f}%"
            df.loc[has_prediction, '简单分析_准确率'] = accuracy_text
            
        except Exception as e:
            print(f"计算准确率时出错: {e}")
        
        return df
    
    def run_test(self):
        """运行测试"""
        print("="*60)
        print("3D彩票预测功能测试")
        print("="*60)
        
        # 读取现有数据
        if not os.path.exists(self.excel_file):
            print("Excel文件不存在")
            return
        
        df = pd.read_excel(self.excel_file)
        print(f"读取到 {len(df)} 期数据")
        
        # 添加预测列
        df = self.add_prediction_columns(df)
        
        # 为最近10期生成预测
        df = self.generate_predictions_for_recent_data(df, num_periods=10)
        
        # 计算准确率
        df = self.calculate_accuracy(df)
        
        # 保存结果
        output_file = os.path.join(self.data_dir, "3d_with_predictions.xlsx")
        df.to_excel(output_file, index=False)
        print(f"\n结果已保存到: {output_file}")
        
        # 显示预测结果
        self.show_prediction_results(df)
        
        return df
    
    def show_prediction_results(self, df):
        """显示预测结果"""
        print("\n" + "="*50)
        print("预测结果展示")
        print("="*50)
        
        has_prediction = df['简单_百位_预测'].notna()
        recent_predictions = df[has_prediction].head(10)
        
        if len(recent_predictions) > 0:
            print(f"{'期号':<10} {'实际号码':<10} {'预测号码':<10} {'百位':<6} {'十位':<6} {'个位':<6}")
            print("-" * 60)
            
            for _, row in recent_predictions.iterrows():
                issue = int(row['期号'])
                actual = f"{int(row['百位'])}{int(row['十位'])}{int(row['个位'])}"
                predicted = row['综合_完整号码'] if pd.notna(row['综合_完整号码']) else "---"
                
                h_mark = "Y" if pd.notna(row['简单_百位_预测']) and int(row['简单_百位_预测']) == int(row['百位']) else "N"
                t_mark = "Y" if pd.notna(row['简单_十位_预测']) and int(row['简单_十位_预测']) == int(row['十位']) else "N"
                u_mark = "Y" if pd.notna(row['简单_个位_预测']) and int(row['简单_个位_预测']) == int(row['个位']) else "N"
                
                print(f"{issue:<10} {actual:<10} {predicted:<10} {h_mark:<6} {t_mark:<6} {u_mark:<6}")

def main():
    """主函数"""
    tester = PredictionTester()
    tester.run_test()

if __name__ == "__main__":
    main()
