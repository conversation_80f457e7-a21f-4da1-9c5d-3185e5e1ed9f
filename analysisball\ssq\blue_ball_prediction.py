#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蓝球数据综合分析与预测
包含多种预测方法：频率分析、马尔科夫链分析、周期性分析、遗漏值分析等
"""

import numpy as np
import pandas as pd
from collections import Counter, defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json

class BlueBallAnalyzer:
    """蓝球综合分析器"""
    
    def __init__(self, data_file_path):
        """初始化分析器"""
        self.data_file_path = data_file_path
        self.raw_data = []
        self.time_ordered_data = []  # 按时间顺序排列的数据，最早的在前面
        self.latest_value = None  # 最新一期的值
        self.states = set()  # 所有可能的状态（1-16）
        
        # 分析结果
        self.frequency_analysis = {}
        self.hot_numbers = []
        self.cold_numbers = []
        self.transition_matrix = {}
        self.transition_probabilities = {}
        self.missing_values = {}
        self.periodic_patterns = {}
        
        # 预测结果
        self.predictions = {}
        self.final_prediction = None
    
    def load_data(self):
        """加载并处理数据"""
        print("正在加载数据...")
        
        with open(self.data_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 提取数字数据
        self.raw_data = []
        for line in lines:
            line = line.strip()
            if line.isdigit():
                self.raw_data.append(int(line))
        
        # 反转数据，使其按时间顺序排列（最早的在前面）
        self.time_ordered_data = list(reversed(self.raw_data))
        
        # 获取最新值
        self.latest_value = self.raw_data[0]
        
        # 获取所有可能的状态（1-16）
        self.states = set(range(1, 17))
        
        print(f"数据加载完成，共 {len(self.time_ordered_data)} 条记录")
        print(f"最新数据（原文件第一行）: {self.latest_value}")
    
    def analyze_frequency(self):
        """频率分析"""
        print("\n正在进行频率分析...")
        
        counter = Counter(self.time_ordered_data)
        total_count = len(self.time_ordered_data)
        
        # 计算每个数字的出现频率
        frequencies = {}
        for number in range(1, 17):
            count = counter.get(number, 0)
            frequency = count / total_count
            frequencies[number] = {
                "count": count,
                "frequency": frequency,
                "percentage": frequency * 100
            }
        
        # 按频率排序
        sorted_freq = sorted(frequencies.items(), key=lambda x: x[1]["frequency"], reverse=True)
        
        # 找出热门数字（出现频率前5的）
        self.hot_numbers = [item[0] for item in sorted_freq[:5]]
        
        # 找出冷门数字（出现频率后5的）
        self.cold_numbers = [item[0] for item in sorted_freq[-5:]]
        
        self.frequency_analysis = frequencies
        
        print("频率分析完成")
        print(f"热门数字（高频）: {self.hot_numbers}")
        print(f"冷门数字（低频）: {self.cold_numbers}")
        
        return frequencies
    
    def build_markov_model(self):
        """构建马尔科夫链模型"""
        print("\n正在构建马尔科夫链模型...")
        
        # 初始化转移计数矩阵
        transition_counts = defaultdict(lambda: defaultdict(int))
        
        # 统计状态转移
        for i in range(len(self.time_ordered_data) - 1):
            current_state = self.time_ordered_data[i]
            next_state = self.time_ordered_data[i + 1]
            transition_counts[current_state][next_state] += 1
        
        # 计算转移概率
        self.transition_probabilities = {}
        for current_state in range(1, 17):
            total_transitions = sum(transition_counts[current_state].values())
            if total_transitions > 0:
                self.transition_probabilities[current_state] = {}
                for next_state in range(1, 17):
                    count = transition_counts[current_state][next_state]
                    probability = count / total_transitions if total_transitions > 0 else 0
                    self.transition_probabilities[current_state][next_state] = probability
            else:
                # 如果某个状态没有转移记录，使用均匀分布
                self.transition_probabilities[current_state] = {
                    state: 1.0 / 16 for state in range(1, 17)
                }
        
        self.transition_matrix = dict(transition_counts)
        
        # 分析当前值的主要转移方向
        if self.latest_value in self.transition_probabilities:
            probs = self.transition_probabilities[self.latest_value]
            top_transitions = sorted(probs.items(), key=lambda x: x[1], reverse=True)[:3]
            
            print(f"当前值 {self.latest_value} 的主要转移方向:")
            for next_state, prob in top_transitions:
                if prob > 0:
                    print(f"  -> {next_state}: {prob:.3f} ({prob*100:.1f}%)")
        
        # 使用马尔科夫链进行预测
        if self.latest_value in self.transition_probabilities:
            probabilities = self.transition_probabilities[self.latest_value]
            self.predictions['markov'] = sorted(probabilities.items(), key=lambda x: x[1], reverse=True)[:5]
            print(f"马尔科夫链预测前5个可能的值: {[p[0] for p in self.predictions['markov']]}")
        
        print("马尔科夫链模型构建完成")
        return self.transition_probabilities
    
    def analyze_missing_values(self):
        """遗漏值分析"""
        print("\n正在进行遗漏值分析...")
        
        missing_values = {}
        last_appearance = {}
        
        # 初始化
        for number in range(1, 17):
            last_appearance[number] = -1
        
        # 从最早的数据开始遍历
        for i, number in enumerate(self.time_ordered_data):
            last_appearance[number] = i
        
        # 计算遗漏值（当前期数与上次出现的期数差）
        current_period = len(self.time_ordered_data) - 1
        for number in range(1, 17):
            if last_appearance[number] == -1:
                missing_value = current_period + 1  # 从未出现过
            else:
                missing_value = current_period - last_appearance[number]
            
            missing_values[number] = missing_value
        
        self.missing_values = missing_values
        
        # 按遗漏值排序
        sorted_missing = sorted(missing_values.items(), key=lambda x: x[1], reverse=True)
        
        print("遗漏值分析完成")
        print("遗漏值最高的5个数字:")
        for number, missing in sorted_missing[:5]:
            print(f"数字 {number}: 遗漏 {missing} 期")
        
        # 遗漏值预测（遗漏值较高的数字可能会在近期出现）
        self.predictions['missing'] = [(num, 1/(miss+1)) for num, miss in sorted(missing_values.items(), key=lambda x: x[1], reverse=True)]
        self.predictions['missing'] = sorted(self.predictions['missing'], key=lambda x: x[1], reverse=True)[:5]
        
        return missing_values
    
    def analyze_recent_trend(self, periods=50):
        """分析最近走势"""
        print(f"\n正在分析最近 {periods} 期走势...")
        
        if len(self.time_ordered_data) < periods:
            periods = len(self.time_ordered_data)
        
        recent_data = self.raw_data[:periods]  # 最近的数据
        
        counter = Counter(recent_data)
        total_count = len(recent_data)
        
        # 计算每个数字的近期出现频率
        recent_frequencies = {}
        for number in range(1, 17):
            count = counter.get(number, 0)
            frequency = count / total_count if total_count > 0 else 0
            recent_frequencies[number] = {
                "count": count,
                "frequency": frequency,
                "percentage": frequency * 100
            }
        
        # 按频率排序
        sorted_freq = sorted(recent_frequencies.items(), key=lambda x: x[1]["frequency"], reverse=True)
        recent_hot = [item[0] for item in sorted_freq[:5]]
        
        print(f"最近 {periods} 期热门数字: {recent_hot}")
        
        # 基于近期走势的预测
        self.predictions['recent_trend'] = [(num, freq["frequency"]) for num, freq in sorted_freq[:5]]
        
        return recent_frequencies
    
    def analyze_periodicity(self, max_lag=20):
        """周期性分析"""
        print("\n正在进行周期性分析...")
        
        # 对每个可能的值进行周期性分析
        periodicity_results = {}
        
        for number in range(1, 17):
            # 创建二元序列，表示该数字在每期是否出现
            binary_series = [1 if x == number else 0 for x in self.time_ordered_data]
            
            # 计算自相关系数
            autocorr = []
            for lag in range(1, min(max_lag + 1, len(binary_series) // 2)):
                correlation = np.corrcoef(binary_series[lag:], binary_series[:-lag])[0, 1]
                autocorr.append(correlation)
            
            # 找出具有最强自相关性的周期
            if autocorr:
                max_correlation_index = np.argmax(np.abs(autocorr))
                max_correlation = autocorr[max_correlation_index]
                period = max_correlation_index + 1
                
                periodicity_results[number] = {
                    "period": period,
                    "correlation": max_correlation
                }
        
        # 筛选出具有明显周期性的数字
        significant_periodicity = {k: v for k, v in periodicity_results.items() if abs(v["correlation"]) > 0.1}
        
        if significant_periodicity:
            print("具有明显周期性的数字:")
            for number, result in significant_periodicity.items():
                print(f"数字 {number}: 周期约为 {result['period']} 期，相关性 {result['correlation']:.3f}")
        else:
            print("未检测到明显的周期性模式")
        
        self.periodic_patterns = periodicity_results
        
        # 使用周期性进行预测
        if significant_periodicity:
            # 基于周期性，预测哪些数字可能在下一期出现
            periodic_predictions = []
            for number, result in significant_periodicity.items():
                period = result["period"]
                # 检查是否与历史周期匹配
                historical_matches = 0
                for i in range(period, len(self.time_ordered_data), period):
                    if i < len(self.time_ordered_data) and self.time_ordered_data[i] == number:
                        historical_matches += 1
                
                match_ratio = historical_matches / (len(self.time_ordered_data) // period) if len(self.time_ordered_data) // period > 0 else 0
                if match_ratio > 0.3:  # 如果历史匹配率较高
                    periodic_predictions.append((number, match_ratio))
            
            # 如果有基于周期性的预测，则排序并取前5
            if periodic_predictions:
                self.predictions['periodic'] = sorted(periodic_predictions, key=lambda x: x[1], reverse=True)[:5]
        
        return periodicity_results
    
    def combine_predictions(self):
        """综合多种预测方法的结果"""
        print("\n正在综合多种预测方法的结果...")
        
        # 初始化综合评分
        combined_scores = {number: 0.0 for number in range(1, 17)}
        
        # 权重设置
        weights = {
            'markov': 0.4,      # 马尔科夫链权重
            'recent_trend': 0.3, # 近期走势权重
            'missing': 0.2,     # 遗漏值权重
            'periodic': 0.1     # 周期性权重
        }
        
        # 结合各种预测结果
        for method, predictions in self.predictions.items():
            if not predictions:
                continue
            
            weight = weights.get(method, 0.1)
            
            # 归一化处理，总分为1
            total_score = sum(score for _, score in predictions)
            if total_score > 0:
                normalized_predictions = [(num, score / total_score) for num, score in predictions]
            else:
                normalized_predictions = [(num, 0) for num, _ in predictions]
            
            # 加权累加到综合评分中
            for number, score in normalized_predictions:
                combined_scores[number] += weight * score
        
        # 再次归一化
        total_combined_score = sum(combined_scores.values())
        if total_combined_score > 0:
            for number in combined_scores:
                combined_scores[number] /= total_combined_score
        
        # 排序结果
        sorted_combined = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        
        print("\n综合评分前5名:")
        for number, score in sorted_combined[:5]:
            print(f"数字 {number}: 分数 {score:.4f} ({score*100:.1f}%)")
        
        # 最终预测结果为综合评分最高的数字
        self.final_prediction = sorted_combined[0][0]
        self.final_prediction_score = sorted_combined[0][1]
        
        # 输出前3个备选预测
        alternative_predictions = [item[0] for item in sorted_combined[1:4]]
        
        print(f"\n最终预测: {self.final_prediction} (置信度: {self.final_prediction_score*100:.1f}%)")
        print(f"备选预测: {alternative_predictions}")
        
        return sorted_combined
    
    def run_analysis(self):
        """运行完整分析流程"""
        print("=" * 50)
        print("开始蓝球数据综合分析...")
        print("=" * 50)
        
        # 1. 加载数据
        self.load_data()
        
        # 2. 频率分析
        self.analyze_frequency()
        
        # 3. 马尔科夫链分析
        self.build_markov_model()
        
        # 4. 遗漏值分析
        self.analyze_missing_values()
        
        # 5. 近期走势分析
        self.analyze_recent_trend()
        
        # 6. 周期性分析
        self.analyze_periodicity()
        
        # 7. 综合预测
        self.combine_predictions()
        
        # 8. 生成报告
        self.generate_report()
        
        print("=" * 50)
        print(f"分析完成！预测下一期蓝球: {self.final_prediction}")
        print("=" * 50)
        
        return self.final_prediction
    
    def generate_report(self):
        """生成分析报告"""
        report = {
            "分析时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "数据统计": {
                "总记录数": len(self.time_ordered_data),
                "最新值": self.latest_value
            },
            "频率分析": {
                "热门数字": self.hot_numbers,
                "冷门数字": self.cold_numbers
            },
            "遗漏值分析": self.missing_values,
            "最终预测": {
                "预测值": self.final_prediction,
                "置信度": round(self.final_prediction_score * 100, 2),
                "备选预测": [item[0] for item in sorted(self.predictions.get('markov', []), key=lambda x: x[1], reverse=True)][:3]
            }
        }
        
        # 保存报告
        report_file = "analysisball/ssq/blue_ball_prediction_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"分析报告已保存到: {report_file}")


def main():
    """主函数"""
    # 创建分析器并运行分析
    analyzer = BlueBallAnalyzer("analysisball/ssq/blueball.txt")
    prediction = analyzer.run_analysis()
    
    return prediction


if __name__ == "__main__":
    main()