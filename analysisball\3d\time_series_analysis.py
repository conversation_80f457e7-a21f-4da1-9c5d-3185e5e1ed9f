#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D彩票时间序列分析模块
功能：
1. 周期性分析 - 检测数字出现的周期性规律
2. 趋势分析 - 分析数字出现频率的长期趋势
3. 季节性分析 - 分析不同时间段的数字分布特征
4. 自相关分析 - 检测数字序列的自相关性
5. 滑动窗口分析 - 分析不同时间窗口的统计特征
"""

import json
import os
from collections import defaultdict, Counter
from typing import Dict, List, Tuple
from datetime import datetime, timedelta
import math

class TimeSeries3DAnalyzer:
    """3D彩票时间序列分析器"""

    def __init__(self, data_file: str = "analysisball/3d/3d.xlsx"):
        """
        初始化时间序列分析器

        Args:
            data_file: 数据文件路径
        """
        self.data_file = data_file
        self.data = []
        self.loaded = False

    def load_data_from_excel(self):
        """从Excel文件加载数据（简化版本）"""
        try:
            print("注意：由于环境限制，使用示例数据进行演示")

            # 生成带时间序列特征的示例数据
            import random
            random.seed(42)

            base_date = datetime(2024, 1, 1)

            for i in range(1000):
                period = f"2024{i:03d}"
                date = base_date + timedelta(days=i)

                # 添加一些时间相关的模式
                day_of_week = date.weekday()
                month = date.month

                # 模拟周期性影响
                hundreds = random.randint(0, 9)
                if day_of_week in [0, 6]:  # 周一和周日
                    hundreds = (hundreds + 1) % 10

                tens = random.randint(0, 9)
                if month in [6, 7, 8]:  # 夏季月份
                    tens = (tens + 2) % 10

                units = random.randint(0, 9)

                self.data.append({
                    'period': period,
                    'date': date.strftime('%Y-%m-%d'),
                    'datetime': date,
                    'hundreds': hundreds,
                    'tens': tens,
                    'units': units,
                    'number_str': f"{hundreds}{tens}{units}",
                    'day_of_week': day_of_week,
                    'month': month,
                    'quarter': (month - 1) // 3 + 1
                })

            self.loaded = True
            print(f"成功加载 {len(self.data)} 条时间序列数据")

        except Exception as e:
            print(f"加载数据失败: {e}")
            raise

    def periodicity_analysis(self, position: str, target_issue: int = None) -> Dict[str, any]:
        """
        周期性分析

        Args:
            position: 位置 ('hundreds', 'tens', 'units')
            target_issue: 目标期号，如果提供，则只分析此期号之前（包含此期号）的数据

        Returns:
            周期性分析结果
        """
        if not self.loaded:
            self.load_data_from_excel()

        original_data = self.data
        if target_issue:
            try:
                # 临时过滤数据
                self.data = [d for d in original_data if int(d['period']) <= target_issue]
                print(f"临时过滤数据进行周期性分析，目标期号: {target_issue}，数据量: {len(self.data)}")
                
                sequence = [item[position] for item in self.data]
            finally:
                # 恢复原始数据
                self.data = original_data
        else:
            sequence = [item[position] for item in self.data]

        # 分析不同周期长度的重复模式
        period_scores = {}

        for period_length in range(2, min(50, len(sequence) // 4)):
            score = 0
            count = 0

            for i in range(period_length, len(sequence)):
                if sequence[i] == sequence[i - period_length]:
                    score += 1
                count += 1

            if count > 0:
                period_scores[period_length] = score / count

        # 找出最显著的周期
        best_periods = sorted(period_scores.items(), key=lambda x: x[1], reverse=True)[:5]

        return {
            'position': position,
            'period_scores': period_scores,
            'best_periods': best_periods,
            'sequence_length': len(sequence)
        }

    def seasonal_analysis(self, target_issue: int = None) -> Dict[str, Dict]:
        """
        季节性分析 - 分析不同时间段的数字分布

        Args:
            target_issue: 目标期号，如果提供，则只分析此期号之前（包含此期号）的数据

        Returns:
            季节性分析结果
        """
        if not self.loaded:
            self.load_data_from_excel()

        original_data = self.data
        try:
            if target_issue:
                # 临时过滤数据
                self.data = [d for d in original_data if int(d['period']) <= target_issue]
                print(f"临时过滤数据进行季节性分析，目标期号: {target_issue}，数据量: {len(self.data)}")

            positions = ['hundreds', 'tens', 'units']
            position_names = ['百位', '十位', '个位']

            results = {}

            for pos, pos_name in zip(positions, position_names):
                # 按星期分析
                weekday_analysis = defaultdict(list)
                for item in self.data:
                    weekday_analysis[item['day_of_week']].append(item[pos])

                weekday_stats = {}
                for day, numbers in weekday_analysis.items():
                    weekday_stats[day] = {
                        'count': len(numbers),
                        'frequency': dict(Counter(numbers)),
                        'most_common': Counter(numbers).most_common(1)[0] if numbers else (0, 0)
                    }

                # 按月份分析
                month_analysis = defaultdict(list)
                for item in self.data:
                    month_analysis[item['month']].append(item[pos])

                month_stats = {}
                for month, numbers in month_analysis.items():
                    month_stats[month] = {
                        'count': len(numbers),
                        'frequency': dict(Counter(numbers)),
                        'most_common': Counter(numbers).most_common(1)[0] if numbers else (0, 0)
                    }

                # 按季度分析
                quarter_analysis = defaultdict(list)
                for item in self.data:
                    quarter_analysis[item['quarter']].append(item[pos])

                quarter_stats = {}
                for quarter, numbers in quarter_analysis.items():
                    quarter_stats[quarter] = {
                        'count': len(numbers),
                        'frequency': dict(Counter(numbers)),
                        'most_common': Counter(numbers).most_common(1)[0] if numbers else (0, 0)
                    }

                results[pos] = {
                    'position_name': pos_name,
                    'weekday_analysis': weekday_stats,
                    'month_analysis': month_stats,
                    'quarter_analysis': quarter_stats
                }

            return results
        finally:
            # 恢复原始数据
            self.data = original_data