{"report_info": {"generated_at": "2025-06-20T10:15:09.183325", "data_source": "analysisball/3d/3d.xlsx", "analysis_modules": ["simple", "markov", "time_series", "ml"]}, "analysis_results": {"simple_analysis": {"summary": {"total_records": 4309, "analysis_date": "2025-06-20", "data_type": "real", "target_issue": 2025155}, "frequency_analysis": {"hundreds": {"position_name": "百位", "frequency": {"6": 458, "4": 420, "5": 404, "2": 445, "9": 419, "0": 404, "3": 449, "8": 437, "7": 458, "1": 415}, "probability": {"6": 0.1062891622186122, "4": 0.09747041076815967, "5": 0.09375725226270597, "2": 0.10327222093293108, "9": 0.09723833836156881, "0": 0.09375725226270597, "3": 0.1042005105592945, "8": 0.10141564168020423, "7": 0.1062891622186122, "1": 0.09631004873520538}, "most_frequent": 6, "least_frequent": 5, "max_frequency": 458, "min_frequency": 404}, "tens": {"position_name": "十位", "frequency": {"7": 425, "5": 426, "3": 424, "0": 434, "8": 473, "2": 379, "1": 434, "4": 449, "6": 432, "9": 433}, "probability": {"7": 0.09863077280111394, "5": 0.0988628452077048, "3": 0.09839870039452309, "0": 0.10071942446043165, "8": 0.10977024831747505, "2": 0.08795544209793456, "1": 0.10071942446043165, "4": 0.1042005105592945, "6": 0.10025527964724994, "9": 0.1004873520538408}, "most_frequent": 8, "least_frequent": 2, "max_frequency": 473, "min_frequency": 379}, "units": {"position_name": "个位", "frequency": {"0": 449, "7": 446, "6": 410, "2": 413, "9": 441, "8": 469, "4": 399, "1": 442, "5": 429, "3": 411}, "probability": {"0": 0.1042005105592945, "7": 0.10350429333952194, "6": 0.0951496867022511, "2": 0.09584590392202368, "9": 0.10234393130656765, "8": 0.10884195869111163, "4": 0.09259689022975168, "1": 0.1025760037131585, "5": 0.09955906242747738, "3": 0.09538175910884196}, "most_frequent": 8, "least_frequent": 4, "max_frequency": 469, "min_frequency": 399}}, "transition_matrices": {"hundreds": {"0": {"0": 0.12623762376237624, "1": 0.09158415841584158, "2": 0.10891089108910891, "3": 0.07920792079207921, "4": 0.12623762376237624, "5": 0.07425742574257425, "6": 0.09900990099009901, "7": 0.10148514851485149, "8": 0.09405940594059406, "9": 0.09900990099009901}, "1": {"0": 0.08433734939759036, "1": 0.10120481927710843, "2": 0.10361445783132531, "3": 0.12530120481927712, "4": 0.06987951807228916, "5": 0.10361445783132531, "6": 0.07228915662650602, "7": 0.09879518072289156, "8": 0.13734939759036144, "9": 0.10361445783132531}, "2": {"0": 0.10337078651685393, "1": 0.09662921348314607, "2": 0.09887640449438202, "3": 0.11910112359550562, "4": 0.10561797752808989, "5": 0.07640449438202247, "6": 0.1101123595505618, "7": 0.10561797752808989, "8": 0.09438202247191012, "9": 0.0898876404494382}, "3": {"0": 0.08908685968819599, "1": 0.08463251670378619, "2": 0.08463251670378619, "3": 0.10467706013363029, "4": 0.111358574610245, "5": 0.12249443207126949, "6": 0.12694877505567928, "7": 0.11581291759465479, "8": 0.0757238307349666, "9": 0.08463251670378619}, "4": {"0": 0.09069212410501193, "1": 0.11217183770883055, "2": 0.08353221957040573, "3": 0.09785202863961814, "4": 0.09069212410501193, "5": 0.09307875894988067, "6": 0.10978520286396182, "7": 0.1026252983293556, "8": 0.1026252983293556, "9": 0.11694510739856802}, "5": {"0": 0.08415841584158416, "1": 0.0891089108910891, "2": 0.12128712871287128, "3": 0.09405940594059406, "4": 0.09405940594059406, "5": 0.08168316831683169, "6": 0.12128712871287128, "7": 0.12871287128712872, "8": 0.10396039603960396, "9": 0.08168316831683169}, "6": {"0": 0.11135371179039301, "1": 0.09170305676855896, "2": 0.11572052401746726, "3": 0.1222707423580786, "4": 0.0851528384279476, "5": 0.07205240174672489, "6": 0.11572052401746726, "7": 0.09170305676855896, "8": 0.09170305676855896, "9": 0.10262008733624454}, "7": {"0": 0.0851528384279476, "1": 0.10480349344978165, "2": 0.10480349344978165, "3": 0.08733624454148471, "4": 0.10698689956331878, "5": 0.08951965065502183, "6": 0.11790393013100436, "7": 0.1091703056768559, "8": 0.10480349344978165, "9": 0.08951965065502183}, "8": {"0": 0.08695652173913043, "1": 0.09382151029748284, "2": 0.10526315789473684, "3": 0.11212814645308924, "4": 0.10068649885583524, "5": 0.11441647597254005, "6": 0.08237986270022883, "7": 0.11212814645308924, "8": 0.09610983981693363, "9": 0.09610983981693363}, "9": {"0": 0.07637231503579953, "1": 0.09785202863961814, "2": 0.10739856801909307, "3": 0.09785202863961814, "4": 0.08353221957040573, "5": 0.10978520286396182, "6": 0.1026252983293556, "7": 0.09785202863961814, "8": 0.11694510739856802, "9": 0.10978520286396182}}, "tens": {"0": {"0": 0.10138248847926268, "1": 0.10138248847926268, "2": 0.07834101382488479, "3": 0.07142857142857142, "4": 0.12442396313364056, "5": 0.10138248847926268, "6": 0.12211981566820276, "7": 0.09907834101382489, "8": 0.11059907834101383, "9": 0.08986175115207373}, "1": {"0": 0.10368663594470046, "1": 0.0967741935483871, "2": 0.07603686635944701, "3": 0.10138248847926268, "4": 0.08525345622119816, "5": 0.08064516129032258, "6": 0.0944700460829493, "7": 0.14516129032258066, "8": 0.1175115207373272, "9": 0.09907834101382489}, "2": {"0": 0.10290237467018469, "1": 0.10026385224274406, "2": 0.10290237467018469, "3": 0.1424802110817942, "4": 0.09762532981530343, "5": 0.12928759894459102, "6": 0.08970976253298153, "7": 0.0712401055408971, "8": 0.08179419525065963, "9": 0.08179419525065963}, "3": {"0": 0.11084905660377359, "1": 0.10377358490566038, "2": 0.10613207547169812, "3": 0.10849056603773585, "4": 0.10377358490566038, "5": 0.09905660377358491, "6": 0.10377358490566038, "7": 0.07783018867924528, "8": 0.09669811320754718, "9": 0.08962264150943396}, "4": {"0": 0.08240534521158129, "1": 0.10022271714922049, "2": 0.0957683741648107, "3": 0.11358574610244988, "4": 0.10690423162583519, "5": 0.060133630289532294, "6": 0.0935412026726058, "7": 0.1091314031180401, "8": 0.12472160356347439, "9": 0.11358574610244988}, "5": {"0": 0.08215962441314555, "1": 0.107981220657277, "2": 0.09859154929577464, "3": 0.09859154929577464, "4": 0.1056338028169014, "5": 0.11971830985915492, "6": 0.08685446009389672, "7": 0.08215962441314555, "8": 0.107981220657277, "9": 0.11032863849765258}, "6": {"0": 0.09722222222222222, "1": 0.09259259259259259, "2": 0.07407407407407407, "3": 0.08101851851851852, "4": 0.10416666666666667, "5": 0.12268518518518519, "6": 0.11574074074074074, "7": 0.08564814814814815, "8": 0.1087962962962963, "9": 0.11805555555555555}, "7": {"0": 0.07294117647058823, "1": 0.1011764705882353, "2": 0.08, "3": 0.09176470588235294, "4": 0.1223529411764706, "5": 0.11058823529411765, "6": 0.10352941176470588, "7": 0.09647058823529411, "8": 0.12, "9": 0.1011764705882353}, "8": {"0": 0.12050739957716702, "1": 0.09725158562367865, "2": 0.09513742071881606, "3": 0.09513742071881606, "4": 0.10570824524312897, "5": 0.08456659619450317, "6": 0.09936575052854123, "7": 0.11205073995771671, "8": 0.09936575052854123, "9": 0.09090909090909091}, "9": {"0": 0.13194444444444445, "1": 0.10648148148148148, "2": 0.07407407407407407, "3": 0.08564814814814815, "4": 0.08564814814814815, "5": 0.08796296296296297, "6": 0.09259259259259259, "7": 0.09953703703703703, "8": 0.12731481481481483, "9": 0.1087962962962963}}, "units": {"0": {"0": 0.11804008908685969, "1": 0.09799554565701558, "2": 0.0757238307349666, "3": 0.08685968819599109, "4": 0.0957683741648107, "5": 0.09131403118040089, "6": 0.07349665924276169, "7": 0.13808463251670378, "8": 0.10467706013363029, "9": 0.11804008908685969}, "1": {"0": 0.11312217194570136, "1": 0.1085972850678733, "2": 0.09954751131221719, "3": 0.10633484162895927, "4": 0.07239819004524888, "5": 0.09728506787330317, "6": 0.10180995475113122, "7": 0.09728506787330317, "8": 0.11764705882352941, "9": 0.08597285067873303}, "2": {"0": 0.10411622276029056, "1": 0.1162227602905569, "2": 0.0847457627118644, "3": 0.09200968523002422, "4": 0.11864406779661017, "5": 0.10411622276029056, "6": 0.09927360774818401, "7": 0.1162227602905569, "8": 0.10653753026634383, "9": 0.05811138014527845}, "3": {"0": 0.10218978102189781, "1": 0.09732360097323602, "2": 0.08759124087591241, "3": 0.10948905109489052, "4": 0.0851581508515815, "5": 0.1070559610705596, "6": 0.10948905109489052, "7": 0.09975669099756691, "8": 0.10218978102189781, "9": 0.09975669099756691}, "4": {"0": 0.08521303258145363, "1": 0.10776942355889724, "2": 0.10025062656641603, "3": 0.09273182957393483, "4": 0.10776942355889724, "5": 0.11528822055137844, "6": 0.08020050125313283, "7": 0.09273182957393483, "8": 0.11027568922305764, "9": 0.10776942355889724}, "5": {"0": 0.09090909090909091, "1": 0.07459207459207459, "2": 0.12354312354312354, "3": 0.10023310023310024, "4": 0.06293706293706294, "5": 0.10722610722610723, "6": 0.11888111888111888, "7": 0.11421911421911422, "8": 0.10023310023310024, "9": 0.10722610722610723}, "6": {"0": 0.1, "1": 0.1073170731707317, "2": 0.08536585365853659, "3": 0.08048780487804878, "4": 0.1024390243902439, "5": 0.10975609756097561, "6": 0.1024390243902439, "7": 0.0951219512195122, "8": 0.11463414634146342, "9": 0.1024390243902439}, "7": {"0": 0.07174887892376682, "1": 0.09417040358744394, "2": 0.09641255605381166, "3": 0.08520179372197309, "4": 0.09865470852017937, "5": 0.10986547085201794, "6": 0.10089686098654709, "7": 0.08968609865470852, "8": 0.13901345291479822, "9": 0.11434977578475336}, "8": {"0": 0.10874200426439233, "1": 0.13219616204690832, "2": 0.10660980810234541, "3": 0.10660980810234541, "4": 0.09808102345415778, "5": 0.0767590618336887, "6": 0.06183368869936034, "7": 0.1044776119402985, "8": 0.10660980810234541, "9": 0.09808102345415778}, "9": {"0": 0.1431818181818182, "1": 0.08863636363636364, "2": 0.09772727272727273, "3": 0.09318181818181819, "4": 0.08636363636363636, "5": 0.08181818181818182, "6": 0.10681818181818181, "7": 0.08636363636363636, "8": 0.08636363636363636, "9": 0.12954545454545455}}}, "pattern_analysis": {"consecutive_patterns": {"ascending": 31, "descending": 38, "same_all": 49, "same_two": 1139, "other": 3052}, "odd_even_patterns": {"偶奇偶": 566, "偶奇奇": 508, "奇奇偶": 528, "偶偶奇": 567, "偶偶偶": 523, "奇偶偶": 523, "奇偶奇": 554, "奇奇奇": 540}, "size_patterns": {"大大小": 518, "小大大": 557, "大大大": 592, "大小小": 542, "小小大": 522, "小大小": 522, "大小大": 524, "小小小": 532}}, "trial_vs_winning_analysis": {"message": "缺少必要的试机号或中奖号码列，跳过分析"}, "predictions": {"hundreds": {"current_number": 4, "predictions": [[9, 0.11694510739856802], [1, 0.11217183770883055], [6, 0.10978520286396182], [7, 0.1026252983293556], [8, 0.1026252983293556]]}, "tens": {"current_number": 9, "predictions": [[0, 0.13194444444444445], [8, 0.12731481481481483], [9, 0.1087962962962963], [1, 0.10648148148148148], [7, 0.09953703703703703]]}, "units": {"current_number": 9, "predictions": [[0, 0.1431818181818182], [9, 0.12954545454545455], [6, 0.10681818181818181], [2, 0.09772727272727273], [3, 0.09318181818181819]]}}}, "markov_analysis": {"analysis_type": "markov_chain", "positions_analyzed": ["hundreds", "tens", "units"], "transition_matrices_built": true, "steady_state_calculated": true, "predictions_generated": true, "note": "这是简化版本的马尔科夫链分析结果"}, "time_series_analysis": {"analysis_type": "time_series", "periodicity_analysis": {"hundreds": {"position": "hundreds", "period_scores": {"2": 0.08817635270541083, "3": 0.11133400200601805, "4": 0.09236947791164658, "5": 0.09949748743718594, "6": 0.08953722334004025, "7": 0.09063444108761329, "8": 0.10383064516129033, "9": 0.09283551967709384, "10": 0.10202020202020202, "11": 0.1122345803842265, "12": 0.0931174089068826, "13": 0.11246200607902736, "14": 0.10649087221095335, "15": 0.09746192893401015, "16": 0.10467479674796748, "17": 0.08646998982706001, "18": 0.09266802443991853, "19": 0.09582059123343527, "20": 0.10306122448979592, "21": 0.09601634320735444, "22": 0.09406952965235174, "23": 0.08904810644831115, "24": 0.09221311475409837, "25": 0.09948717948717949, "26": 0.10574948665297741, "27": 0.10688591983556012, "28": 0.08539094650205761, "29": 0.08959835221421215, "30": 0.10618556701030928, "31": 0.1001031991744066, "32": 0.11776859504132231, "33": 0.08583247156153051, "34": 0.09627329192546584, "35": 0.0849740932642487, "36": 0.11307053941908714, "37": 0.10384215991692627, "38": 0.08004158004158005, "39": 0.1040582726326743, "40": 0.0875, "41": 0.09071949947862357, "42": 0.1022964509394572, "43": 0.08463949843260188, "44": 0.0899581589958159, "45": 0.10261780104712041, "46": 0.10272536687631027, "47": 0.11332633788037776, "48": 0.08823529411764706, "49": 0.11146161934805468}, "best_periods": [[32, 0.11776859504132231], [47, 0.11332633788037776], [36, 0.11307053941908714], [13, 0.11246200607902736], [11, 0.1122345803842265]], "sequence_length": 1000}, "tens": {"position": "tens", "period_scores": {"2": 0.09118236472945891, "3": 0.10832497492477432, "4": 0.09136546184738956, "5": 0.09346733668341708, "6": 0.096579476861167, "7": 0.10473313192346426, "8": 0.11088709677419355, "9": 0.08980827447023208, "10": 0.1101010101010101, "11": 0.08998988877654196, "12": 0.0860323886639676, "13": 0.11043566362715299, "14": 0.09026369168356999, "15": 0.09238578680203045, "16": 0.0975609756097561, "17": 0.10986775178026449, "18": 0.09470468431771895, "19": 0.109072375127421, "20": 0.11428571428571428, "21": 0.09295199182839632, "22": 0.09611451942740286, "23": 0.09007164790174002, "24": 0.09938524590163934, "25": 0.09948717948717949, "26": 0.1026694045174538, "27": 0.09352517985611511, "28": 0.08024691358024691, "29": 0.11534500514933059, "30": 0.09587628865979382, "31": 0.09391124871001032, "32": 0.11880165289256199, "33": 0.10237849017580145, "34": 0.10559006211180125, "35": 0.0922279792746114, "36": 0.10892116182572614, "37": 0.0976116303219107, "38": 0.10083160083160084, "39": 0.09261186264308012, "40": 0.09895833333333333, "41": 0.12095933263816476, "42": 0.11169102296450939, "43": 0.09717868338557993, "44": 0.0899581589958159, "45": 0.09528795811518324, "46": 0.09014675052410902, "47": 0.07660020986358866, "48": 0.09033613445378151, "49": 0.10410094637223975}, "best_periods": [[41, 0.12095933263816476], [32, 0.11880165289256199], [29, 0.11534500514933059], [20, 0.11428571428571428], [42, 0.11169102296450939]], "sequence_length": 1000}, "units": {"position": "units", "period_scores": {"2": 0.08917835671342686, "3": 0.10531594784353059, "4": 0.12048192771084337, "5": 0.1135678391959799, "6": 0.1056338028169014, "7": 0.1067472306143001, "8": 0.1159274193548387, "9": 0.10393541876892028, "10": 0.10202020202020202, "11": 0.09201213346814964, "12": 0.0860323886639676, "13": 0.09219858156028368, "14": 0.08924949290060852, "15": 0.10558375634517767, "16": 0.09247967479674797, "17": 0.10376398779247202, "18": 0.09674134419551934, "19": 0.09785932721712538, "20": 0.10612244897959183, "21": 0.09805924412665985, "22": 0.10429447852760736, "23": 0.09109518935516889, "24": 0.09733606557377049, "25": 0.10974358974358975, "26": 0.09240246406570841, "27": 0.10174717368961973, "28": 0.09362139917695474, "29": 0.08650875386199794, "30": 0.0979381443298969, "31": 0.11351909184726522, "32": 0.09607438016528926, "33": 0.10444674250258532, "34": 0.10248447204968944, "35": 0.11191709844559586, "36": 0.11410788381742738, "37": 0.10176531671858775, "38": 0.10291060291060292, "39": 0.09469302809573361, "40": 0.09479166666666666, "41": 0.09593326381647549, "42": 0.08663883089770355, "43": 0.0961337513061651, "44": 0.09205020920502092, "45": 0.08586387434554973, "46": 0.09853249475890985, "47": 0.08289611752360965, "48": 0.11974789915966387, "49": 0.08727655099894847}, "best_periods": [[4, 0.12048192771084337], [48, 0.11974789915966387], [8, 0.1159274193548387], [36, 0.11410788381742738], [5, 0.1135678391959799]], "sequence_length": 1000}}, "seasonal_analysis": {"hundreds": {"position_name": "百位", "weekday_analysis": {"0": {"count": 143, "frequency": {"2": 13, "9": 16, "5": 14, "4": 15, "7": 14, "8": 17, "3": 17, "0": 12, "1": 15, "6": 10}, "most_common": [8, 17]}, "1": {"count": 143, "frequency": {"3": 13, "7": 16, "8": 12, "2": 12, "6": 13, "5": 18, "1": 13, "9": 19, "4": 17, "0": 10}, "most_common": [9, 19]}, "2": {"count": 143, "frequency": {"1": 15, "0": 13, "3": 17, "8": 18, "5": 14, "2": 15, "7": 14, "9": 9, "6": 10, "4": 18}, "most_common": [8, 18]}, "3": {"count": 143, "frequency": {"9": 15, "5": 15, "3": 17, "0": 12, "6": 15, "7": 11, "2": 17, "1": 16, "4": 13, "8": 12}, "most_common": [3, 17]}, "4": {"count": 143, "frequency": {"0": 13, "3": 16, "7": 16, "2": 13, "5": 24, "8": 12, "1": 14, "6": 13, "9": 9, "4": 13}, "most_common": [5, 24]}, "5": {"count": 143, "frequency": {"3": 16, "1": 14, "6": 23, "2": 15, "0": 20, "7": 14, "4": 12, "8": 6, "5": 11, "9": 12}, "most_common": [6, 23]}, "6": {"count": 142, "frequency": {"1": 11, "6": 13, "4": 18, "9": 11, "0": 21, "5": 12, "2": 13, "8": 13, "3": 12, "7": 18}, "most_common": [0, 21]}}, "month_analysis": {"1": {"count": 93, "frequency": {"2": 6, "3": 14, "1": 11, "9": 7, "0": 9, "7": 9, "5": 10, "6": 7, "8": 7, "4": 13}, "most_common": [3, 14]}, "2": {"count": 85, "frequency": {"3": 14, "7": 8, "2": 13, "9": 3, "5": 13, "8": 7, "0": 13, "1": 4, "6": 5, "4": 5}, "most_common": [3, 14]}, "3": {"count": 93, "frequency": {"8": 13, "6": 13, "9": 6, "4": 5, "5": 5, "3": 10, "0": 7, "2": 14, "7": 10, "1": 10}, "most_common": [2, 14]}, "4": {"count": 90, "frequency": {"5": 12, "9": 13, "8": 7, "2": 9, "4": 13, "1": 10, "3": 8, "7": 7, "0": 8, "6": 3}, "most_common": [9, 13]}, "5": {"count": 93, "frequency": {"1": 8, "0": 12, "2": 8, "7": 11, "9": 6, "8": 9, "5": 8, "3": 11, "6": 11, "4": 9}, "most_common": [0, 12]}, "6": {"count": 90, "frequency": {"6": 8, "9": 13, "7": 9, "4": 9, "3": 11, "5": 5, "0": 12, "8": 6, "2": 11, "1": 6}, "most_common": [9, 13]}, "7": {"count": 93, "frequency": {"0": 10, "8": 10, "7": 10, "3": 8, "4": 10, "6": 12, "2": 9, "1": 11, "5": 7, "9": 6}, "most_common": [6, 12]}, "8": {"count": 93, "frequency": {"0": 7, "5": 15, "4": 10, "3": 11, "7": 8, "9": 6, "1": 12, "6": 11, "8": 7, "2": 6}, "most_common": [5, 15]}, "9": {"count": 86, "frequency": {"5": 12, "9": 10, "3": 8, "8": 8, "7": 10, "1": 7, "6": 7, "0": 8, "4": 8, "2": 8}, "most_common": [5, 12]}, "10": {"count": 62, "frequency": {"1": 9, "8": 8, "9": 5, "7": 5, "0": 6, "4": 10, "3": 4, "6": 9, "2": 2, "5": 4}, "most_common": [4, 10]}, "11": {"count": 60, "frequency": {"2": 6, "7": 8, "6": 5, "5": 8, "4": 9, "0": 6, "1": 5, "8": 2, "3": 3, "9": 8}, "most_common": [4, 9]}, "12": {"count": 62, "frequency": {"3": 6, "8": 6, "7": 8, "5": 9, "1": 5, "9": 8, "6": 6, "0": 3, "2": 6, "4": 5}, "most_common": [5, 9]}}, "quarter_analysis": {"1": {"count": 271, "frequency": {"2": 33, "3": 38, "1": 25, "9": 16, "0": 29, "7": 27, "5": 28, "6": 25, "8": 27, "4": 23}, "most_common": [3, 38]}, "2": {"count": 273, "frequency": {"5": 25, "9": 32, "8": 22, "2": 28, "4": 31, "1": 24, "3": 30, "7": 27, "0": 32, "6": 22}, "most_common": [9, 32]}, "3": {"count": 272, "frequency": {"0": 25, "8": 25, "7": 28, "3": 27, "4": 28, "6": 30, "2": 23, "1": 30, "5": 34, "9": 22}, "most_common": [5, 34]}, "4": {"count": 184, "frequency": {"1": 19, "8": 16, "9": 21, "7": 21, "0": 15, "4": 24, "3": 13, "6": 20, "2": 14, "5": 21}, "most_common": [4, 24]}}}, "tens": {"position_name": "十位", "weekday_analysis": {"0": {"count": 143, "frequency": {"0": 21, "6": 7, "7": 20, "9": 9, "8": 19, "2": 12, "3": 18, "5": 10, "4": 13, "1": 14}, "most_common": [0, 21]}, "1": {"count": 143, "frequency": {"3": 17, "9": 15, "1": 11, "5": 11, "4": 10, "8": 18, "7": 14, "0": 16, "2": 12, "6": 19}, "most_common": [6, 19]}, "2": {"count": 143, "frequency": {"8": 14, "2": 16, "4": 15, "9": 18, "7": 18, "1": 12, "3": 15, "6": 18, "0": 11, "5": 6}, "most_common": [9, 18]}, "3": {"count": 143, "frequency": {"6": 16, "4": 15, "5": 14, "2": 14, "7": 18, "1": 16, "0": 17, "3": 10, "8": 11, "9": 12}, "most_common": [7, 18]}, "4": {"count": 143, "frequency": {"1": 15, "5": 11, "2": 17, "6": 11, "4": 19, "3": 10, "8": 23, "0": 7, "9": 17, "7": 13}, "most_common": [8, 23]}, "5": {"count": 143, "frequency": {"8": 18, "6": 17, "4": 16, "3": 19, "5": 14, "1": 15, "7": 12, "2": 13, "9": 11, "0": 8}, "most_common": [3, 19]}, "6": {"count": 142, "frequency": {"8": 18, "5": 11, "1": 15, "4": 14, "7": 16, "3": 20, "2": 8, "9": 13, "0": 14, "6": 13}, "most_common": [3, 20]}}, "month_analysis": {"1": {"count": 93, "frequency": {"0": 8, "3": 9, "8": 10, "6": 10, "1": 8, "9": 6, "2": 8, "4": 14, "5": 13, "7": 7}, "most_common": [4, 14]}, "2": {"count": 85, "frequency": {"7": 12, "2": 8, "3": 8, "4": 8, "9": 9, "1": 10, "6": 8, "8": 12, "0": 8, "5": 2}, "most_common": [7, 12]}, "3": {"count": 93, "frequency": {"3": 13, "5": 10, "7": 12, "0": 8, "9": 8, "6": 12, "1": 7, "2": 6, "8": 11, "4": 6}, "most_common": [3, 13]}, "4": {"count": 90, "frequency": {"3": 10, "8": 12, "0": 12, "7": 5, "2": 7, "1": 11, "9": 7, "6": 9, "4": 12, "5": 5}, "most_common": [8, 12]}, "5": {"count": 93, "frequency": {"6": 11, "0": 9, "4": 6, "8": 9, "2": 12, "5": 8, "3": 8, "1": 12, "7": 12, "9": 6}, "most_common": [2, 12]}, "6": {"count": 90, "frequency": {"2": 4, "5": 7, "3": 13, "7": 12, "0": 12, "8": 11, "1": 9, "6": 4, "9": 11, "4": 7}, "most_common": [3, 13]}, "7": {"count": 93, "frequency": {"0": 7, "9": 10, "6": 11, "5": 5, "3": 11, "4": 11, "8": 15, "2": 7, "1": 7, "7": 9}, "most_common": [8, 15]}, "8": {"count": 93, "frequency": {"0": 10, "5": 4, "2": 10, "3": 14, "9": 12, "4": 9, "6": 9, "8": 7, "1": 12, "7": 6}, "most_common": [3, 14]}, "9": {"count": 86, "frequency": {"8": 12, "3": 9, "6": 7, "7": 10, "4": 9, "5": 9, "9": 7, "2": 7, "0": 6, "1": 10}, "most_common": [8, 12]}, "10": {"count": 62, "frequency": {"3": 6, "6": 2, "9": 8, "4": 10, "0": 4, "1": 4, "2": 11, "7": 7, "5": 2, "8": 8}, "most_common": [2, 11]}, "11": {"count": 60, "frequency": {"9": 3, "1": 6, "6": 12, "7": 10, "4": 7, "8": 7, "0": 3, "5": 4, "3": 4, "2": 4}, "most_common": [6, 12]}, "12": {"count": 62, "frequency": {"7": 9, "5": 8, "8": 7, "9": 8, "6": 6, "2": 8, "1": 2, "0": 7, "4": 3, "3": 4}, "most_common": [7, 9]}}, "quarter_analysis": {"1": {"count": 271, "frequency": {"0": 24, "3": 30, "8": 33, "6": 30, "1": 25, "9": 23, "2": 22, "4": 28, "5": 25, "7": 31}, "most_common": [8, 33]}, "2": {"count": 273, "frequency": {"3": 31, "8": 32, "0": 33, "7": 29, "2": 23, "1": 32, "9": 24, "6": 24, "4": 25, "5": 20}, "most_common": [0, 33]}, "3": {"count": 272, "frequency": {"0": 23, "9": 29, "6": 27, "5": 18, "3": 34, "4": 29, "8": 34, "2": 24, "1": 29, "7": 25}, "most_common": [3, 34]}, "4": {"count": 184, "frequency": {"3": 14, "6": 20, "9": 19, "4": 20, "0": 14, "1": 12, "2": 23, "7": 26, "5": 14, "8": 22}, "most_common": [7, 26]}}}, "units": {"position_name": "个位", "weekday_analysis": {"0": {"count": 143, "frequency": {"4": 7, "3": 17, "7": 13, "5": 15, "6": 11, "0": 15, "2": 11, "1": 21, "8": 23, "9": 10}, "most_common": [8, 23]}, "1": {"count": 143, "frequency": {"2": 14, "4": 16, "6": 13, "5": 21, "1": 14, "8": 17, "9": 17, "3": 12, "7": 9, "0": 10}, "most_common": [5, 21]}, "2": {"count": 143, "frequency": {"1": 14, "6": 12, "4": 20, "5": 13, "8": 14, "9": 21, "7": 18, "2": 11, "0": 10, "3": 10}, "most_common": [9, 21]}, "3": {"count": 143, "frequency": {"0": 16, "2": 13, "9": 13, "8": 17, "6": 15, "7": 16, "1": 13, "3": 14, "4": 13, "5": 13}, "most_common": [8, 17]}, "4": {"count": 143, "frequency": {"3": 13, "1": 16, "0": 14, "7": 13, "4": 22, "9": 10, "8": 9, "5": 15, "2": 13, "6": 18}, "most_common": [4, 22]}, "5": {"count": 143, "frequency": {"9": 17, "1": 13, "8": 14, "6": 13, "7": 14, "2": 14, "5": 19, "4": 15, "3": 19, "0": 5}, "most_common": [5, 19]}, "6": {"count": 142, "frequency": {"3": 9, "9": 23, "6": 10, "0": 13, "8": 15, "7": 17, "1": 15, "4": 16, "5": 16, "2": 8}, "most_common": [9, 23]}}, "month_analysis": {"1": {"count": 93, "frequency": {"4": 14, "2": 7, "1": 11, "0": 8, "3": 10, "9": 11, "6": 6, "7": 9, "5": 10, "8": 7}, "most_common": [4, 14]}, "2": {"count": 85, "frequency": {"6": 5, "4": 10, "8": 10, "9": 9, "2": 10, "1": 9, "0": 3, "7": 6, "5": 15, "3": 8}, "most_common": [5, 15]}, "3": {"count": 93, "frequency": {"4": 6, "7": 12, "1": 22, "9": 8, "8": 9, "5": 6, "0": 9, "3": 6, "2": 6, "6": 9}, "most_common": [1, 22]}, "4": {"count": 90, "frequency": {"0": 10, "8": 13, "2": 6, "1": 3, "9": 13, "5": 8, "6": 7, "7": 6, "4": 12, "3": 12}, "most_common": [8, 13]}, "5": {"count": 93, "frequency": {"4": 10, "5": 15, "2": 8, "6": 11, "1": 7, "8": 11, "9": 11, "3": 7, "0": 8, "7": 5}, "most_common": [5, 15]}, "6": {"count": 90, "frequency": {"8": 10, "5": 13, "1": 10, "4": 9, "6": 8, "2": 6, "9": 7, "7": 9, "3": 9, "0": 9}, "most_common": [5, 13]}, "7": {"count": 93, "frequency": {"6": 14, "2": 10, "4": 10, "8": 10, "5": 10, "1": 8, "3": 11, "7": 11, "9": 7, "0": 2}, "most_common": [6, 14]}, "8": {"count": 93, "frequency": {"0": 7, "1": 5, "3": 9, "9": 15, "2": 5, "4": 9, "6": 14, "8": 9, "5": 10, "7": 10}, "most_common": [9, 15]}, "9": {"count": 86, "frequency": {"0": 7, "1": 12, "7": 11, "3": 7, "4": 12, "5": 13, "2": 9, "8": 5, "6": 4, "9": 6}, "most_common": [5, 13]}, "10": {"count": 62, "frequency": {"1": 7, "9": 13, "3": 5, "7": 4, "2": 7, "8": 7, "0": 8, "4": 7, "6": 2, "5": 2}, "most_common": [9, 13]}, "11": {"count": 60, "frequency": {"6": 3, "7": 13, "5": 7, "2": 5, "8": 7, "1": 4, "3": 6, "9": 2, "0": 7, "4": 6}, "most_common": [7, 13]}, "12": {"count": 62, "frequency": {"8": 11, "5": 3, "6": 9, "3": 4, "0": 5, "7": 4, "1": 8, "2": 5, "4": 4, "9": 9}, "most_common": [8, 11]}}, "quarter_analysis": {"1": {"count": 271, "frequency": {"4": 30, "2": 23, "1": 42, "0": 20, "3": 24, "9": 28, "6": 20, "7": 27, "5": 31, "8": 26}, "most_common": [1, 42]}, "2": {"count": 273, "frequency": {"0": 27, "8": 34, "2": 20, "1": 20, "9": 31, "5": 36, "6": 26, "7": 20, "4": 31, "3": 28}, "most_common": [5, 36]}, "3": {"count": 272, "frequency": {"6": 32, "2": 24, "4": 31, "8": 24, "5": 33, "1": 25, "3": 27, "7": 32, "9": 28, "0": 16}, "most_common": [5, 33]}, "4": {"count": 184, "frequency": {"1": 19, "9": 24, "3": 15, "7": 21, "2": 17, "8": 25, "0": 20, "4": 17, "6": 14, "5": 12}, "most_common": [8, 25]}}}}}, "ml_prediction": {"analysis_type": "machine_learning", "features_extracted": 4298, "models_used": ["naive_bayes", "decision_tree", "neural_network"], "predictions": {"hundreds": {"naive_bayes": {"0": 0.12623762376237624, "1": 0.0915841584158416, "2": 0.10891089108910891, "3": 0.07920792079207921, "4": 0.12623762376237624, "5": 0.07425742574257425, "6": 0.09900990099009901, "7": 0.10148514851485148, "8": 0.09405940594059406, "9": 0.09900990099009901}, "decision_tree": {"0": 0.09433962264150944, "1": 0.12264150943396226, "2": 0.09433962264150944, "3": 0.09433962264150944, "4": 0.09433962264150944, "5": 0.09433962264150944, "6": 0.09433962264150944, "7": 0.09433962264150944, "8": 0.09433962264150944, "9": 0.12264150943396226}, "neural_network": {"0": 0.08, "1": 0.09999999999999998, "2": 0.16, "3": 0.09999999999999998, "4": 0.08, "5": 0.08, "6": 0.08, "7": 0.09999999999999998, "8": 0.11999999999999997, "9": 0.09999999999999998}}, "tens": {"naive_bayes": {"0": 0.09722222222222221, "1": 0.09259259259259257, "2": 0.07407407407407404, "3": 0.0810185185185185, "4": 0.10416666666666663, "5": 0.12268518518518516, "6": 0.11574074074074071, "7": 0.08564814814814813, "8": 0.10879629629629627, "9": 0.11805555555555552}, "decision_tree": {"0": 0.09433962264150944, "1": 0.09433962264150944, "2": 0.09433962264150944, "3": 0.09433962264150944, "4": 0.09433962264150944, "5": 0.12264150943396226, "6": 0.09433962264150944, "7": 0.12264150943396226, "8": 0.09433962264150944, "9": 0.09433962264150944}, "neural_network": {"0": 0.08000000000000002, "1": 0.14, "2": 0.12, "3": 0.12, "4": 0.08000000000000002, "5": 0.1, "6": 0.12, "7": 0.060000000000000005, "8": 0.060000000000000005, "9": 0.12}}, "units": {"naive_bayes": {"0": 0.1435079726651481, "1": 0.08883826879271073, "2": 0.09794988610478361, "3": 0.09339407744874716, "4": 0.0865603644646925, "5": 0.08200455580865605, "6": 0.1070615034168565, "7": 0.0865603644646925, "8": 0.08428246013667426, "9": 0.12984054669703876}, "decision_tree": {"0": 0.12264150943396226, "1": 0.09433962264150944, "2": 0.09433962264150944, "3": 0.09433962264150944, "4": 0.09433962264150944, "5": 0.09433962264150944, "6": 0.09433962264150944, "7": 0.09433962264150944, "8": 0.12264150943396226, "9": 0.09433962264150944}, "neural_network": {"0": 0.08000000000000002, "1": 0.1, "2": 0.08000000000000002, "3": 0.1, "4": 0.08000000000000002, "5": 0.060000000000000005, "6": 0.14, "7": 0.1, "8": 0.16000000000000003, "9": 0.1}}}}}, "summary": {"total_analyses_completed": 4, "available_analyzers": 4, "analysis_status": {"simple_analysis": "completed", "markov_analysis": "completed", "time_series_analysis": "completed", "ml_prediction": "completed"}}}