#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D彩票马尔科夫链分析模块
功能：
1. 构建马尔科夫链转移矩阵
2. 分析百位、十位、个位数字的状态转移概率
3. 计算稳态分布
4. 预测下期可能出现的数字
5. 分析数字序列的马尔科夫性质
"""

import numpy as np
import pandas as pd
import sqlite3
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Tuple, Optional
from pathlib import Path
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class MarkovChain3D:
    """3D彩票马尔科夫链分析类"""

    def __init__(self, data_source: str = "analysisball/3d/3d_data.db"):
        """
        初始化马尔科夫链分析器

        Args:
            data_source: 数据源路径（SQLite数据库或Excel文件）
        """
        self.data_source = Path(data_source)
        self.transition_matrices = {}
        self.steady_states = {}
        self.data = None

    def load_data(self) -> pd.DataFrame:
        """加载3D彩票数据"""
        try:
            if self.data_source.suffix == '.db':
                # 从SQLite数据库加载
                with sqlite3.connect(self.data_source) as conn:
                    query = '''
                        SELECT period, date, hundreds, tens, units, number_str
                        FROM lottery_3d
                        ORDER BY period ASC
                    '''
                    self.data = pd.read_sql_query(query, conn)
            elif self.data_source.suffix in ['.xlsx', '.xls']:
                # 从Excel文件加载
                self.data = pd.read_excel(self.data_source)
                # 确保列名一致
                if '期号' in self.data.columns:
                    self.data = self.data.rename(columns={
                        '期号': 'period', '日期': 'date',
                        '百位': 'hundreds', '十位': 'tens', '个位': 'units'
                    })
            else:
                raise ValueError(f"不支持的文件格式: {self.data_source.suffix}")

            logger.info(f"成功加载 {len(self.data)} 条3D彩票数据")
            return self.data

        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            raise

    def build_transition_matrix(self, position: str, order: int = 1) -> np.ndarray:
        """
        构建指定位置的马尔科夫链转移矩阵

        Args:
            position: 位置 ('hundreds', 'tens', 'units')
            order: 马尔科夫链的阶数（默认为1阶）

        Returns:
            转移矩阵
        """
        if self.data is None:
            self.load_data()

        # 获取指定位置的数字序列
        sequence = self.data[position].values

        if order == 1:
            # 一阶马尔科夫链
            transition_matrix = np.zeros((10, 10))

            for i in range(len(sequence) - 1):
                current_state = sequence[i]
                next_state = sequence[i + 1]
                transition_matrix[current_state][next_state] += 1

            # 归一化为概率矩阵
            row_sums = transition_matrix.sum(axis=1)
            for i in range(10):
                if row_sums[i] > 0:
                    transition_matrix[i] = transition_matrix[i] / row_sums[i]

        elif order == 2:
            # 二阶马尔科夫链
            transition_matrix = np.zeros((100, 10))

            for i in range(len(sequence) - 2):
                current_state = sequence[i] * 10 + sequence[i + 1]
                next_state = sequence[i + 2]
                transition_matrix[current_state][next_state] += 1

            # 归一化为概率矩阵
            row_sums = transition_matrix.sum(axis=1)
            for i in range(100):
                if row_sums[i] > 0:
                    transition_matrix[i] = transition_matrix[i] / row_sums[i]

        else:
            raise ValueError("目前只支持1阶和2阶马尔科夫链")

        self.transition_matrices[f"{position}_order_{order}"] = transition_matrix
        logger.info(f"构建了{position}位置的{order}阶马尔科夫链转移矩阵")

        return transition_matrix

    def calculate_steady_state(self, position: str, order: int = 1) -> np.ndarray:
        """
        计算马尔科夫链的稳态分布

        Args:
            position: 位置
            order: 阶数

        Returns:
            稳态分布向量
        """
        matrix_key = f"{position}_order_{order}"

        if matrix_key not in self.transition_matrices:
            self.build_transition_matrix(position, order)

        transition_matrix = self.transition_matrices[matrix_key]

        # 计算特征值和特征向量
        eigenvalues, eigenvectors = np.linalg.eig(transition_matrix.T)

        # 找到特征值为1的特征向量（稳态分布）
        steady_state_index = np.argmax(np.real(eigenvalues))
        steady_state = np.real(eigenvectors[:, steady_state_index])

        # 归一化
        steady_state = steady_state / np.sum(steady_state)

        self.steady_states[matrix_key] = steady_state

        return steady_state

    def predict_next_numbers(self, position: str, current_state: int, order: int = 1, top_k: int = 3) -> List[Tuple[int, float]]:
        """
        基于马尔科夫链预测下期可能出现的数字

        Args:
            position: 位置
            current_state: 当前状态
            order: 阶数
            top_k: 返回概率最高的前k个数字

        Returns:
            [(数字, 概率), ...] 按概率降序排列
        """
        matrix_key = f"{position}_order_{order}"

        if matrix_key not in self.transition_matrices:
            self.build_transition_matrix(position, order)

        transition_matrix = self.transition_matrices[matrix_key]

        if order == 1:
            probabilities = transition_matrix[current_state]
        elif order == 2:
            probabilities = transition_matrix[current_state]

        # 获取概率最高的前k个数字
        top_indices = np.argsort(probabilities)[::-1][:top_k]
        predictions = [(int(idx), float(probabilities[idx])) for idx in top_indices]

        return predictions

    def test_markov_property(self, position: str) -> Dict[str, float]:
        """
        测试数字序列的马尔科夫性质

        Args:
            position: 位置

        Returns:
            测试结果字典
        """
        if self.data is None:
            self.load_data()

        sequence = self.data[position].values

        # 计算条件概率
        # P(X_n+1 = j | X_n = i) vs P(X_n+1 = j | X_n = i, X_n-1 = k)

        # 一阶条件概率
        first_order_counts = np.zeros((10, 10))
        for i in range(len(sequence) - 1):
            current = sequence[i]
            next_val = sequence[i + 1]
            first_order_counts[current][next_val] += 1

        # 二阶条件概率
        second_order_counts = np.zeros((10, 10, 10))
        for i in range(len(sequence) - 2):
            prev = sequence[i]
            current = sequence[i + 1]
            next_val = sequence[i + 2]
            second_order_counts[prev][current][next_val] += 1

        # 计算卡方统计量
        chi_square = 0
        degrees_of_freedom = 0

        for i in range(10):
            for j in range(10):
                if first_order_counts[i].sum() > 0:
                    expected_first = first_order_counts[i][j]

                    for k in range(10):
                        if second_order_counts[k][i].sum() > 0:
                            observed = second_order_counts[k][i][j]
                            expected = expected_first * (second_order_counts[k][i].sum() / first_order_counts[i].sum())

                            if expected > 0:
                                chi_square += (observed - expected) ** 2 / expected
                                degrees_of_freedom += 1

        # 计算p值（简化版本）
        from scipy import stats
        p_value = 1 - stats.chi2.cdf(chi_square, degrees_of_freedom)

        return {
            'chi_square': chi_square,
            'degrees_of_freedom': degrees_of_freedom,
            'p_value': p_value,
            'is_markov': p_value > 0.05  # 显著性水平0.05
        }

    def analyze_transition_patterns(self, position: str) -> Dict[str, any]:
        """
        分析转移模式

        Args:
            position: 位置

        Returns:
            分析结果
        """
        if self.data is None:
            self.load_data()

        sequence = self.data[position].values

        # 分析自转移概率（数字保持不变的概率）
        self_transitions = np.zeros(10)
        total_transitions = np.zeros(10)

        for i in range(len(sequence) - 1):
            current = sequence[i]
            next_val = sequence[i + 1]
            total_transitions[current] += 1
            if current == next_val:
                self_transitions[current] += 1

        self_transition_probs = np.divide(self_transitions, total_transitions,
                                        out=np.zeros_like(self_transitions),
                                        where=total_transitions!=0)

        # 分析最常见的转移
        transition_counts = {}
        for i in range(len(sequence) - 1):
            transition = (sequence[i], sequence[i + 1])
            transition_counts[transition] = transition_counts.get(transition, 0) + 1

        # 获取最频繁的转移
        most_common_transitions = sorted(transition_counts.items(),
                                       key=lambda x: x[1], reverse=True)[:10]

        return {
            'self_transition_probabilities': self_transition_probs.tolist(),
            'most_common_transitions': most_common_transitions,
            'total_transitions': len(sequence) - 1
        }

    def visualize_transition_matrix(self, position: str, order: int = 1, save_path: Optional[str] = None):
        """
        可视化转移矩阵

        Args:
            position: 位置
            order: 阶数
            save_path: 保存路径
        """
        matrix_key = f"{position}_order_{order}"

        if matrix_key not in self.transition_matrices:
            self.build_transition_matrix(position, order)

        transition_matrix = self.transition_matrices[matrix_key]

        plt.figure(figsize=(12, 10))

        if order == 1:
            sns.heatmap(transition_matrix,
                       annot=True,
                       fmt='.3f',
                       cmap='YlOrRd',
                       xticklabels=range(10),
                       yticklabels=range(10))
            plt.title(f'{position}位数字一阶马尔科夫链转移矩阵')
            plt.xlabel('下期数字')
            plt.ylabel('当前数字')

        elif order == 2:
            # 二阶矩阵太大，只显示部分
            plt.imshow(transition_matrix, cmap='YlOrRd', aspect='auto')
            plt.title(f'{position}位数字二阶马尔科夫链转移矩阵')
            plt.xlabel('下期数字')
            plt.ylabel('当前状态(前两位数字)')
            plt.colorbar()

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

    def generate_comprehensive_report(self, output_dir: str = "analysisball/3d/reports"):
        """
        生成综合分析报告

        Args:
            output_dir: 输出目录
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        if self.data is None:
            self.load_data()

        report = {
            'data_summary': {
                'total_records': len(self.data),
                'date_range': {
                    'start': self.data['date'].min(),
                    'end': self.data['date'].max()
                }
            },
            'markov_analysis': {}
        }

        positions = ['hundreds', 'tens', 'units']
        position_names = ['百位', '十位', '个位']

        for pos, pos_name in zip(positions, position_names):
            print(f"\n分析{pos_name}数字的马尔科夫性质...")

            # 构建转移矩阵
            matrix_1 = self.build_transition_matrix(pos, order=1)
            matrix_2 = self.build_transition_matrix(pos, order=2)

            # 计算稳态分布
            steady_1 = self.calculate_steady_state(pos, order=1)

            # 测试马尔科夫性质
            markov_test = self.test_markov_property(pos)

            # 分析转移模式
            patterns = self.analyze_transition_patterns(pos)

            # 预测示例（基于最后一个数字）
            last_number = self.data[pos].iloc[-1]
            predictions = self.predict_next_numbers(pos, last_number, order=1, top_k=5)

            report['markov_analysis'][pos] = {
                'markov_test': markov_test,
                'steady_state_distribution': steady_1.tolist(),
                'transition_patterns': patterns,
                'predictions_for_last_number': {
                    'current_number': int(last_number),
                    'top_predictions': predictions
                }
            }

            # 生成可视化
            self.visualize_transition_matrix(pos, order=1,
                                           save_path=output_path / f'{pos}_transition_matrix.png')

        # 保存报告
        import json
        with open(output_path / 'markov_analysis_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"\n分析报告已保存到: {output_path}")
        return report

def main():
    """主函数 - 演示马尔科夫链分析"""
    # 设置日志
    logging.basicConfig(level=logging.INFO,
                       format='%(asctime)s - %(levelname)s - %(message)s')

    # 创建分析器实例
    # 如果有SQLite数据库，使用数据库；否则使用Excel文件
    data_source = "analysisball/3d/3d_data.db"
    if not Path(data_source).exists():
        data_source = "analysisball/3d/3d.xlsx"

    analyzer = MarkovChain3D(data_source)

    try:
        # 生成综合报告
        report = analyzer.generate_comprehensive_report()

        # 打印关键结果
        print("\n=== 3D彩票马尔科夫链分析结果 ===")

        for position in ['hundreds', 'tens', 'units']:
            pos_name = {'hundreds': '百位', 'tens': '十位', 'units': '个位'}[position]
            analysis = report['markov_analysis'][position]

            print(f"\n{pos_name}分析结果:")
            print(f"  马尔科夫性质测试: {'通过' if analysis['markov_test']['is_markov'] else '不通过'}")
            print(f"  p值: {analysis['markov_test']['p_value']:.4f}")

            # 显示稳态分布
            steady_state = analysis['steady_state_distribution']
            print(f"  稳态分布 (数字0-9的长期概率):")
            for i, prob in enumerate(steady_state):
                print(f"    数字{i}: {prob:.3f}")

            # 显示预测结果
            predictions = analysis['predictions_for_last_number']['top_predictions']
            current = analysis['predictions_for_last_number']['current_number']
            print(f"  基于当前数字{current}的预测:")
            for num, prob in predictions:
                print(f"    数字{num}: {prob:.3f}")

    except Exception as e:
        logger.error(f"分析过程中出错: {e}")
        print(f"错误: {e}")

if __name__ == "__main__":
    main()