import pygame
import random
import sys
import math
from config import *

class SnakeGame:
    def __init__(self):
        pygame.init()
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("贪吃蛇游戏")
        self.clock = pygame.time.Clock()
        
        # 加载字体
        self.fonts = {}
        for size_name, size in FONT_SIZES.items():
            try:
                # 尝试使用系统中文字体
                self.fonts[size_name] = pygame.font.Font("C:/Windows/Fonts/msyh.ttc", size)
            except:
                # 如果没有中文字体，使用默认字体
                self.fonts[size_name] = pygame.font.Font(None, size)
        
        self.reset_game()
        self.game_state = GAME_STATES['MENU']
        
    def reset_game(self):
        """重置游戏状态"""
        # 蛇的初始位置 (游戏区域中心)
        center_x = GAME_AREA_WIDTH // (2 * GRID_SIZE)
        center_y = GAME_AREA_HEIGHT // (2 * GRID_SIZE)
        
        self.snake = [(center_x, center_y)]
        self.direction = DIRECTIONS['RIGHT']
        self.food = self.generate_food()
        self.score = 0
        self.speed = INITIAL_SPEED
        
    def generate_food(self):
        """生成食物位置"""
        while True:
            x = random.randint(0, GAME_AREA_WIDTH // GRID_SIZE - 1)
            y = random.randint(0, GAME_AREA_HEIGHT // GRID_SIZE - 1)
            if (x, y) not in self.snake:
                return (x, y)
    
    def draw_rounded_rect(self, surface, color, rect, radius):
        """绘制圆角矩形"""
        pygame.draw.rect(surface, color, rect, border_radius=radius)
    
    def draw_text(self, text, font_size, color, x, y, center=False):
        """绘制文字"""
        font = self.fonts[font_size]
        text_surface = font.render(text, True, color)
        if center:
            text_rect = text_surface.get_rect(center=(x, y))
            self.screen.blit(text_surface, text_rect)
        else:
            self.screen.blit(text_surface, (x, y))
    
    def draw_button(self, text, x, y, width, height, color, text_color):
        """绘制按钮"""
        button_rect = pygame.Rect(x, y, width, height)
        self.draw_rounded_rect(self.screen, color, button_rect, BUTTON_RADIUS)
        
        # 绘制按钮文字
        font = self.fonts['button']
        text_surface = font.render(text, True, text_color)
        text_rect = text_surface.get_rect(center=button_rect.center)
        self.screen.blit(text_surface, text_rect)
        
        return button_rect
    
    def draw_menu(self):
        """绘制主菜单"""
        self.screen.fill(COLORS['background'])
        
        # 标题
        self.draw_text("贪吃蛇游戏", 'title', COLORS['text_primary'], 
                      WINDOW_WIDTH // 2, 150, center=True)
        
        # 开始按钮
        start_button = self.draw_button("开始游戏", 
                                      WINDOW_WIDTH // 2 - BUTTON_WIDTH // 2, 
                                      250, BUTTON_WIDTH, BUTTON_HEIGHT,
                                      COLORS['button'], COLORS['text_primary'])
        
        # 退出按钮
        quit_button = self.draw_button("退出游戏", 
                                     WINDOW_WIDTH // 2 - BUTTON_WIDTH // 2, 
                                     320, BUTTON_WIDTH, BUTTON_HEIGHT,
                                     COLORS['button'], COLORS['text_primary'])
        
        # 游戏说明
        instructions = [
            "使用方向键控制蛇的移动",
            "吃到红色食物可以增长身体",
            "不要撞到墙壁或自己的身体",
            "按空格键暂停/继续游戏"
        ]
        
        for i, instruction in enumerate(instructions):
            self.draw_text(instruction, 'small', COLORS['text_secondary'],
                          WINDOW_WIDTH // 2, 400 + i * 25, center=True)
        
        return start_button, quit_button
    
    def draw_game(self):
        """绘制游戏界面"""
        self.screen.fill(COLORS['background'])
        
        # 绘制游戏区域背景
        game_area_rect = pygame.Rect(GAME_AREA_X, GAME_AREA_Y, 
                                   GAME_AREA_WIDTH, GAME_AREA_HEIGHT)
        self.draw_rounded_rect(self.screen, COLORS['game_area'], game_area_rect, 10)
        
        # 绘制游戏区域边框
        pygame.draw.rect(self.screen, COLORS['border'], game_area_rect, 2, border_radius=10)
        
        # 绘制蛇
        for i, (x, y) in enumerate(self.snake):
            rect = pygame.Rect(GAME_AREA_X + x * GRID_SIZE, 
                             GAME_AREA_Y + y * GRID_SIZE, 
                             GRID_SIZE - 1, GRID_SIZE - 1)
            
            if i == 0:  # 蛇头
                self.draw_rounded_rect(self.screen, COLORS['snake_head'], rect, 6)
            else:  # 蛇身
                self.draw_rounded_rect(self.screen, COLORS['snake_body'], rect, 4)
        
        # 绘制食物
        food_x, food_y = self.food
        food_rect = pygame.Rect(GAME_AREA_X + food_x * GRID_SIZE, 
                              GAME_AREA_Y + food_y * GRID_SIZE, 
                              GRID_SIZE - 1, GRID_SIZE - 1)
        self.draw_rounded_rect(self.screen, COLORS['food'], food_rect, 8)
        
        # 绘制分数
        self.draw_text(f"分数: {self.score}", 'score', COLORS['text_primary'], 50, 30)
        
        # 绘制速度
        self.draw_text(f"速度: {self.speed:.1f}", 'score', COLORS['text_primary'], 50, 60)
        
        # 如果游戏暂停，显示暂停信息
        if self.game_state == GAME_STATES['PAUSED']:
            overlay = pygame.Surface((WINDOW_WIDTH, WINDOW_HEIGHT))
            overlay.set_alpha(128)
            overlay.fill((0, 0, 0))
            self.screen.blit(overlay, (0, 0))

            self.draw_text("游戏暂停", 'title', COLORS['text_primary'],
                          WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2, center=True)
            self.draw_text("按空格键继续", 'button', COLORS['text_secondary'],
                          WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 + 50, center=True)

    def draw_game_over(self):
        """绘制游戏结束界面"""
        self.screen.fill(COLORS['background'])

        # 游戏结束标题
        self.draw_text("游戏结束", 'title', COLORS['text_primary'],
                      WINDOW_WIDTH // 2, 200, center=True)

        # 最终分数
        self.draw_text(f"最终分数: {self.score}", 'score', COLORS['text_primary'],
                      WINDOW_WIDTH // 2, 250, center=True)

        # 重新开始按钮
        restart_button = self.draw_button("重新开始",
                                        WINDOW_WIDTH // 2 - BUTTON_WIDTH // 2,
                                        320, BUTTON_WIDTH, BUTTON_HEIGHT,
                                        COLORS['button'], COLORS['text_primary'])

        # 返回主菜单按钮
        menu_button = self.draw_button("返回主菜单",
                                     WINDOW_WIDTH // 2 - BUTTON_WIDTH // 2,
                                     380, BUTTON_WIDTH, BUTTON_HEIGHT,
                                     COLORS['button'], COLORS['text_primary'])

        return restart_button, menu_button

    def update_snake(self):
        """更新蛇的位置"""
        if self.game_state != GAME_STATES['PLAYING']:
            return

        # 计算新的头部位置
        head_x, head_y = self.snake[0]
        dx, dy = self.direction
        new_head = (head_x + dx, head_y + dy)

        # 检查边界碰撞
        if (new_head[0] < 0 or new_head[0] >= GAME_AREA_WIDTH // GRID_SIZE or
            new_head[1] < 0 or new_head[1] >= GAME_AREA_HEIGHT // GRID_SIZE):
            self.game_state = GAME_STATES['GAME_OVER']
            return

        # 检查自身碰撞
        if new_head in self.snake:
            self.game_state = GAME_STATES['GAME_OVER']
            return

        # 添加新头部
        self.snake.insert(0, new_head)

        # 检查是否吃到食物
        if new_head == self.food:
            self.score += 10
            self.speed = min(self.speed + SPEED_INCREMENT, MAX_SPEED)
            self.food = self.generate_food()
        else:
            # 如果没有吃到食物，移除尾部
            self.snake.pop()

    def handle_events(self):
        """处理事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False

            elif event.type == pygame.KEYDOWN:
                if self.game_state == GAME_STATES['PLAYING']:
                    # 方向控制
                    if event.key == pygame.K_UP and self.direction != DIRECTIONS['DOWN']:
                        self.direction = DIRECTIONS['UP']
                    elif event.key == pygame.K_DOWN and self.direction != DIRECTIONS['UP']:
                        self.direction = DIRECTIONS['DOWN']
                    elif event.key == pygame.K_LEFT and self.direction != DIRECTIONS['RIGHT']:
                        self.direction = DIRECTIONS['LEFT']
                    elif event.key == pygame.K_RIGHT and self.direction != DIRECTIONS['LEFT']:
                        self.direction = DIRECTIONS['RIGHT']
                    elif event.key == pygame.K_SPACE:
                        self.game_state = GAME_STATES['PAUSED']

                elif self.game_state == GAME_STATES['PAUSED']:
                    if event.key == pygame.K_SPACE:
                        self.game_state = GAME_STATES['PLAYING']

            elif event.type == pygame.MOUSEBUTTONDOWN:
                mouse_pos = pygame.mouse.get_pos()

                if self.game_state == GAME_STATES['MENU']:
                    start_button, quit_button = self.draw_menu()
                    if start_button.collidepoint(mouse_pos):
                        self.reset_game()
                        self.game_state = GAME_STATES['PLAYING']
                    elif quit_button.collidepoint(mouse_pos):
                        return False

                elif self.game_state == GAME_STATES['GAME_OVER']:
                    restart_button, menu_button = self.draw_game_over()
                    if restart_button.collidepoint(mouse_pos):
                        self.reset_game()
                        self.game_state = GAME_STATES['PLAYING']
                    elif menu_button.collidepoint(mouse_pos):
                        self.game_state = GAME_STATES['MENU']

        return True

    def run(self):
        """游戏主循环"""
        running = True

        while running:
            # 处理事件
            running = self.handle_events()

            # 更新游戏状态
            if self.game_state == GAME_STATES['PLAYING']:
                self.update_snake()

            # 绘制界面
            if self.game_state == GAME_STATES['MENU']:
                self.draw_menu()
            elif self.game_state in [GAME_STATES['PLAYING'], GAME_STATES['PAUSED']]:
                self.draw_game()
            elif self.game_state == GAME_STATES['GAME_OVER']:
                self.draw_game_over()

            pygame.display.flip()
            self.clock.tick(self.speed)

        pygame.quit()
        sys.exit()


def main():
    """主函数"""
    try:
        game = SnakeGame()
        game.run()
    except Exception as e:
        print(f"游戏运行出错: {e}")
        pygame.quit()
        sys.exit()


if __name__ == "__main__":
    main()
