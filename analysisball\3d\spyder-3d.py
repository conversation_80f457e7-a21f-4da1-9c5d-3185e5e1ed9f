import requests
import pandas as pd
import os
import time
import json
import re
from bs4 import BeautifulSoup
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from datetime import datetime


def get_lottery_data(last_period=None, num_periods=100):
    base_url = "https://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36",
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Referer": "https://www.cwl.gov.cn/ygkj/wqkjgg/3d/",
        "X-Requested-With": "XMLHttpRequest"
    }
    
    data = []
    page = 1
    page_size = 100
    
    session = requests.Session()
    retry = Retry(total=3, backoff_factor=0.1, status_forcelist=[500, 502, 503, 504])
    adapter = HTTPAdapter(max_retries=retry)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    
    while len(data) < num_periods:
        params = {
            "name": "3d",
            "issueCount": "",
            "issueStart": "",
            "issueEnd": "",
            "pageNo": page,
            "pageSize": page_size,
            "systemType": "PC"
        }
        
        try:
            response = session.get(base_url, headers=headers, params=params, timeout=10)
            response.raise_for_status()
        except requests.exceptions.RequestException as e:
            print(f"页面 {page} 请求失败: {e}")
            break
        
        try:
            json_data = response.json()
        except json.JSONDecodeError:
            print(f"页面 {page} 返回的不是有效的JSON")
            break
        
        draw_notice_list = json_data.get("result", [])
        
        if not draw_notice_list:
            print(f"页面 {page} 没有找到数据")
            break
        
        for item in draw_notice_list:
            date = item.get("date")
            period = item.get("code")
            red = item.get("red", "").split(",")
            
            if len(red) == 3:
                if last_period and int(period) <= int(last_period):
                    return data
                num1, num2, num3 = red
                data.append([date, period, num1, num2, num3, red])
            
            if len(data) >= num_periods:
                return data
        
        page += 1
        # time.sleep(1)  # 小的延迟以避免频繁请求
    
    return data

def save_to_excel(data, filename):
    # 创建基本数据框
    new_df = pd.DataFrame(data, columns=["日期", "期号", "百位", "十位", "个位", "奖号"])
    # 确保新数据中的期号为数值类型
    new_df["期号"] = pd.to_numeric(new_df["期号"])
    
    # 如果已存在Excel文件，合并数据
    if os.path.exists(filename):
        existing_df = pd.read_excel(filename)
        # 确保已存在数据中的期号为数值类型
        existing_df["期号"] = pd.to_numeric(existing_df["期号"])
        
        # 合并数据
        merged_df = pd.concat([new_df, existing_df])
        
        # 去除重复期号，保留第一个出现的（即新数据）
        result_df = merged_df.drop_duplicates(subset=["期号"], keep="first")
        
        # 再次对数据按期号降序排序
        combined_df = result_df.sort_values(by="期号", ascending=False)
    else:
        combined_df = new_df
    
    combined_df.to_excel(filename, index=False)
    print(f"数据已保存到 {filename}")
    print(f"总数据条数: {len(combined_df)}")

def main():
    save_dir = os.path.join("analysisball", "3d")
    os.makedirs(save_dir, exist_ok=True)
    filename = "3d.xlsx"
    full_path = os.path.join(save_dir, filename)

    last_period = None
    if os.path.exists(full_path):
        existing_df = pd.read_excel(full_path)
        if not existing_df.empty:
            last_period = existing_df["期号"].max()

    lottery_data = get_lottery_data(last_period, num_periods=5000)  # 获取最新的5000期数据
    if lottery_data:
        save_to_excel(lottery_data, full_path)
    else:
        print("没有新数据需要更新。")

if __name__ == "__main__":
    main()
